"""Test helpers for Texas Hold'em poker tests."""

from __future__ import annotations

from typing import Any

import pytest
from pydantic import BaseModel

from common.utils.utils import is_dict, is_list
from libs.common.common.core.app_error import AppException, ErrorConfig
from libs.game.game_api.core import GameType, PlayerId
from libs.game.texas_holdem.poker import (
    BettingRound,
    Card,
    CardRank,
    CardSuit,
    PlayerStatus,
    TexasHoldemAction,
    TexasHoldemConfig,
    TexasHoldemEnv,
    TexasHoldemMove,
    TexasHoldemPlayer,
    TexasHoldemState,
)


class PokerTest:
    """Instance-based test class for testing poker games through TexasHoldemRuleManager.

    Each test should create its own instance using PokerTest.setup().
    The test instance manages game state and config internally.
    """

    state: TexasHoldemState
    config: TexasHoldemConfig
    env: TexasHoldemEnv
    last_state_diff: TexasHoldemStateDiff | None = None

    def __init__(self, state: TexasHoldemState, config: TexasHoldemConfig, env: TexasHoldemEnv) -> None:
        """Initialize the poker test helper with state and config.

        Use PokerTest.setup() instead of calling this directly.
        """
        self.state = state
        self.config = config
        self.env = env

    @classmethod
    def create(
        cls,
        # Game configuration
        num_players: int = 5,
        small_blind: int = 5,
        big_blind: int = 10,
        starting_chips: int = 1000,
        min_raise: int | None = None,
        max_raise: int | None = None,
        # Player overrides
        chips: dict[PlayerId, int] | None = None,
        current_bets: dict[PlayerId, int] | None = None,
        total_bets: dict[PlayerId, int] | None = None,
        hole_cards: dict[PlayerId, list[Card]] | None = None,
        # State overrides (for advanced testing scenarios)
        betting_round: BettingRound | None = None,
        current_player_id: PlayerId | None = None,
        community_cards: list[Card] | None = None,
        pot: int | None = None,
        current_bet: int | None = None,
        dealer_position: int | None = -3,
        deck: list[Card] | None = None,
        winners: list[PlayerId] | None = None,
        winning_hands: dict[PlayerId, Any] | None = None,
    ) -> PokerTest:
        """Set up a poker test helper.

        Args:
            num_players: Number of players (default: 5)
            betting_round: Current betting round (default: PREFLOP)
            small_blind: Small blind amount (default: 5)
            big_blind: Big blind amount (default: 10)
            starting_chips: Starting chips per player (default: 1000)
            ante: Ante amount (default: 0)
            min_raise: Minimum raise amount (default: None)
            max_raise: Maximum raise amount (default: None)

            # Player overrides (applied after production init)
            chips: Override chips for specific players {PLAYER_1: 500}
            current_bets: Override current bets for specific players
            total_bets: Override total bets for specific players
            hole_cards: Override hole cards for specific players {PLAYER_1: [card1, card2]}

            # State overrides (for advanced testing scenarios only)
            current_player_id: Current player ID (default: from production init)
            community_cards: Community cards (default: from production init)
            pot: Pot amount (default: from production init)
            current_bet: Current bet amount (default: from production init)
            dealer_position: Dealer position (default: from production init)
            deck: Deck of cards (default: from production init)
            winners: Winners list (default: [])
            winning_hands: Winning hands dict (default: {})

        Returns:
            PokerTest instance with configured state and config
        """
        if num_players < 2:
            raise ValueError("Number of players must be at least 2")
        if num_players > 5:
            raise ValueError("Number of players must be at most 5")

        # Create config
        config = TexasHoldemConfig(
            game_type=GameType.TEXAS_HOLDEM,
            max_players=5,
            min_players=2,
            small_blind=small_blind,
            big_blind=big_blind,
            starting_chips=starting_chips,
            starting_chips_override_for_test=chips,
            default_dealer_position=(dealer_position % num_players) if dealer_position is not None else 0,
            min_raise=min_raise,
            max_raise=max_raise,
        )

        env = TexasHoldemEnv()
        player_ids = [PlayerId(f"player_{i + 1}") for i in range(num_players)]
        state = env.init(config, player_ids)

        # Apply basic overrides
        if current_player_id is not None:
            state.current_player_id = current_player_id

        # Apply player-specific overrides to the initialized state
        if current_bets:
            for player_id, player_current_bet in current_bets.items():
                player = state.get_player_by_id(player_id)
                player.current_bet = player_current_bet

        if total_bets:
            for player_id, player_total_bet in total_bets.items():
                player = state.get_player_by_id(player_id)
                player.total_bet = player_total_bet

        # Apply hole cards overrides (remove from deck to prevent duplicates)
        if hole_cards:
            cards_to_remove = []
            for player_id, player_hole_cards in hole_cards.items():
                player = state.get_player_by_id(player_id)
                player.hole_cards = player_hole_cards.copy()
                cards_to_remove.extend(player_hole_cards)
            # Remove manually set cards from deck
            state.deck = [card for card in state.deck if card not in cards_to_remove]

        # Apply additional state overrides
        if betting_round is not None:
            state.betting_round = betting_round
        if current_bet is not None:
            state.current_bet = current_bet
        if pot is not None:
            state.pot = pot
        if community_cards is not None:
            state.community_cards = community_cards.copy()
        if deck is not None:
            state.deck = deck.copy()
        if winners is not None:
            state.winners = winners.copy()
        if winning_hands is not None:
            state.winning_hands = winning_hands.copy()

        return cls(state, config, env)

    @staticmethod
    def _create_full_deck() -> list[Card]:
        """Create a full 52-card deck using CardRank/CardSuit enums."""
        suits = [CardSuit.HEARTS, CardSuit.DIAMONDS, CardSuit.CLUBS, CardSuit.SPADES]
        ranks = [
            CardRank.TWO,
            CardRank.THREE,
            CardRank.FOUR,
            CardRank.FIVE,
            CardRank.SIX,
            CardRank.SEVEN,
            CardRank.EIGHT,
            CardRank.NINE,
            CardRank.TEN,
            CardRank.JACK,
            CardRank.QUEEN,
            CardRank.KING,
            CardRank.ACE,
        ]
        return [Card(rank=r, suit=s) for s in suits for r in ranks]

    def process_move(
        self,
        player_id: PlayerId,
        action: TexasHoldemAction,
        amount: int | None = None,
        expected_state_diff: TexasHoldemStateDiff | None = None,
    ) -> None:
        """Process a poker move through the TexasHoldemRuleManager.

        Updates the internal state and returns the new state.
        Asserts that the move succeeds.
        Use process_move_error() for testing error conditions.

        Args:
            player_id: ID of the player making the move
            action: Action to take (use TexasHoldemAction enum)
            amount: Bet/raise amount (required for RAISE)
            expected_state_diff: Expected state diff (use TexasHoldemStateDiff)

        Returns:
            The new state after applying the move

        Raises:
            AssertionError: If the move fails
        """

        move = TexasHoldemMove(
            player_id=player_id,
            action=action,
            amount=amount,
        )

        prev_state = self.state
        new_state = self.env.apply_move(self.state, move, self.config)
        self.last_state_diff = self._compute_state_diff(prev_state, new_state)
        self.state = new_state
        if expected_state_diff:
            self.assert_state_change(expected_state_diff)

    def process_move_error(
        self,
        player_id: PlayerId,
        action: TexasHoldemAction,
        amount: int | None = None,
        expected_error: ErrorConfig | None = None,
    ) -> AppException:
        """Process a poker move that is expected to fail.

        Does not update internal state.
        Asserts that the move fails and returns the error.
        Use process_move() for testing successful moves.

        Args:
            player_id: ID of the player making the move
            action: Action to take (use TexasHoldemAction enum)
            amount: Bet/raise amount (required for RAISE)
            expected_error: Expected error (use ErrorConfig)

        Returns:
            The error that occurred

        Raises:
            AssertionError: If the move succeeds when it should fail
        """

        move = TexasHoldemMove(
            player_id=player_id,
            action=action,
            amount=amount,
        )

        # Try to apply the move - it should fail with an exception
        with pytest.raises(AppException) as e:
            _ = self.env.apply_move(self.state, move, self.config)
        if expected_error:
            assert e.value.details.code == expected_error.code, f"Expected error code {expected_error.code}, got {e.value.details.code}"
        return e.value

    # Getter methods for accessing state properties
    def get_player(self, player_id: PlayerId | str) -> TexasHoldemPlayer:
        """Get a player by ID."""
        player = self.state.get_player_by_id(PlayerId(player_id))
        assert player is not None, f"Player {player_id} not found"
        return player

    def get_pot(self) -> int:
        """Get the current pot amount."""
        return self.state.pot

    def get_betting_round(self) -> BettingRound:
        """Get the current betting round."""
        return self.state.betting_round

    def is_game_finished(self) -> bool:
        """Check if the game is finished."""
        return self.state.is_finished

    def get_winners(self) -> list[PlayerId]:
        """Get the list of winners from the current state."""
        return self.state.winners

    def advance_to_showdown(self) -> None:
        """Advance the game to showdown using only env.apply_move.

        This method makes all remaining active players check through all betting rounds
        until the game naturally reaches showdown. This is the proper way to advance
        the game without implementing game logic in the test framework.
        """
        # If the game is already finished, don't do anything
        if self.state.is_finished:
            return

        # Set neutral community cards that don't interfere with hole card rankings
        # This ensures deterministic hand evaluation for testing
        # Use cards that won't create straights, flushes, or other strong hands
        if len(self.state.community_cards) == 0:
            self.state.community_cards = [
                Card(rank=CardRank.TWO, suit=CardSuit.DIAMONDS),
                Card(rank=CardRank.FIVE, suit=CardSuit.CLUBS),
                Card(rank=CardRank.NINE, suit=CardSuit.SPADES),
                Card(rank=CardRank.JACK, suit=CardSuit.HEARTS),
                Card(rank=CardRank.KING, suit=CardSuit.DIAMONDS),
            ]
            # Remove these cards from the deck to prevent duplicates
            for card in self.state.community_cards:
                if card in self.state.deck:
                    self.state.deck.remove(card)

        # Keep making moves until the game is finished
        max_iterations = 100  # Safety limit to prevent infinite loops
        iterations = 0

        while not self.state.is_finished and iterations < max_iterations:
            iterations += 1

            # Get the current player who needs to act
            if self.state.current_player_id is None:
                break

            current_player_id = self.state.current_player_id

            # If all remaining players are all-in, the game should automatically advance
            active_players = self.state.get_active_players()
            if len(active_players) == 0:
                # All players are either folded or all-in, game should be finished
                break

            # Make the current player check (or call if there's a bet)
            if self.state.current_bet > 0:
                # There's a bet to call
                current_player = self.state.get_player_by_id(current_player_id)
                if current_player and current_player.current_bet < self.state.current_bet:
                    # Player needs to call
                    self.process_move(current_player_id, TexasHoldemAction.CALL)
                else:
                    # Player can check
                    self.process_move(current_player_id, TexasHoldemAction.CHECK)
            else:
                # No bet, player can check
                self.process_move(current_player_id, TexasHoldemAction.CHECK)

        if iterations >= max_iterations:
            raise RuntimeError("advance_to_showdown exceeded maximum iterations - possible infinite loop")

    def get_player_status(self, player_id: PlayerId) -> PlayerStatus:
        """Get a player's status."""
        player = self.state.get_player_by_id(player_id)
        assert player is not None, f"Player {player_id} not found"
        return player.status

    def get_community_cards(self) -> list[Card]:
        """Get community cards."""
        return self.state.community_cards

    def get_total_chips_in_play(self) -> int:
        """Get the total chips in play (player chips + total bets)."""
        return sum(player.chips + player.total_bet for player in self.state.players)

    def assert_side_pots_count(self, expected_count: int) -> None:
        """Assert number of side pots."""
        side_pots_count = len(self.state.side_pots)
        assert side_pots_count == expected_count, f"Side pots count is {side_pots_count}, expected {expected_count}"

    def assert_total_chips_conservation(self, expected_total: int) -> None:
        """Assert that total chips in play are conserved."""
        total_player_chips = sum(player.chips for player in self.state.players)
        total_in_pot = self.state.pot + sum(
            side_pot.amount for side_pot in self.state.side_pots
        )
        total_current_bets = sum(player.current_bet for player in self.state.players)
        actual_total = total_player_chips + total_in_pot + total_current_bets
        assert actual_total == expected_total, f"Total chips is {actual_total}, expected {expected_total}"

    def get_side_pots_count(self) -> int:
        """Get the number of side pots."""
        return len(self.state.side_pots)

    def assert_player_status(self, player_id: PlayerId, expected_status: PlayerStatus) -> None:
        """Assert a player's status."""
        player = self.state.get_player_by_id(player_id)
        assert player is not None, f"Player {player_id} not found"
        assert player.status == expected_status, f"Player {player_id} status is {player.status}, expected {expected_status}"

    def assert_state_change(
        self,
        expected: TexasHoldemStateDiff,
        exclude_fields: set[str] | None = {"players.hole_cards", "deck", "community_cards"},  # noqa: B006
    ) -> None:
        """Assert that the last processed move produced exactly the given state delta.

        This is a strong, deep, and exact assertion:
        - The computed actual diff must be equal to `expected` (no missing or extra changes).
        - Applying `expected` on the previous state must reproduce the current state exactly.

        Args:
            expected: The expected state diff
            exclude_fields: Optional set of field names to exclude from comparison
        """
        assert self.last_state_diff is not None, "No last state diff recorded. Call process_move() first."

        # Exact diff equality using Pydantic dumps (exclude None)
        expected_dump = expected.model_dump(exclude_none=True)
        actual_dump = self.last_state_diff.model_dump(exclude_none=True)

        # Remove excluded fields from both dumps
        if exclude_fields:
            self._remove_nested_fields(expected_dump, exclude_fields)
            self._remove_nested_fields(actual_dump, exclude_fields)

        assert actual_dump == expected_dump, f"State diff mismatch.\nExpected: {expected_dump}\nActual:   {actual_dump}"

    def assert_state(
        self,
        expected: TexasHoldemState,
        exclude_fields: set[str] | None = {"players.hole_cards", "deck", "community_cards"},  # noqa: B006
    ) -> None:
        """Assert that the current game state matches the expected state exactly.

        This is a strong, deep, and exact assertion that compares the full game state.
        Use this for testing edge cases of blinds, after complex side pot payouts,
        or when you need to verify the complete game state.

        Args:
            expected: The expected full game state
            exclude_fields: Optional set of field names to exclude from comparison
        """

        def serialize(value: Any) -> Any:
            # Use pydantic dumps for BaseModel, and recursively for lists of BaseModels
            if isinstance(value, BaseModel):
                return value.model_dump()
            if is_list(value):
                return [serialize(v) for v in value]
            if is_dict(value):
                return {k: serialize(v) for k, v in value.items()}
            return value

        # Get serialized versions of both states
        expected_dump = expected.model_dump(exclude_none=True)
        actual_dump = self.state.model_dump(exclude_none=True)

        # Remove excluded fields from both dumps
        if exclude_fields:
            self._remove_nested_fields(expected_dump, exclude_fields)
            self._remove_nested_fields(actual_dump, exclude_fields)

        assert actual_dump == expected_dump, f"State mismatch.\nExpected: {expected_dump}\nActual:   {actual_dump}"

    def _remove_nested_fields(self, data: dict[str, Any], fields: set[str]) -> None:
        for field in list(fields):
            if "." in field:
                # Handle nested field exclusion like "players.chips"
                parent, child = field.split(".", 1)
                if parent in data:
                    if is_dict(data[parent]):
                        for key in list(data[parent].keys()):
                            if isinstance(data[parent][key], dict) and child in data[parent][key]:
                                data[parent][key].pop(child, None)
                                # Remove empty player diffs
                                if not data[parent][key]:
                                    data[parent].pop(key, None)
                    elif is_list(data[parent]):
                        for item in data[parent]:
                            if is_dict(item) and child in item:
                                item.pop(child, None)
                                # Remove empty player diffs
                                if not item:
                                    data[parent].remove(item)
                    else:
                        raise ValueError(f"Unsupported type for field {field}: {type(data[parent])}")

                    # Remove empty players dict
                    if not data[parent]:
                        data.pop(parent, None)
            else:
                # Handle top-level field exclusion
                data.pop(field, None)

    def _compute_state_diff(self, old: TexasHoldemState, new: TexasHoldemState) -> TexasHoldemStateDiff:
        def serialize(value: Any) -> Any:
            # Use pydantic dumps for BaseModel, and recursively for lists of BaseModels
            if isinstance(value, BaseModel):
                return value.model_dump()
            if is_list(value):
                return [serialize(v) for v in value]
            if is_dict(value):
                return {k: serialize(v) for k, v in value.items()}
            return value

        kwargs: dict[str, Any] = {}

        # Special handling for players: compute per-player diffs by id
        old_players = {p.player_id: p for p in old.players}
        new_players = {p.player_id: p for p in new.players}
        players_diff: dict[PlayerId, TexasHoldemPlayerDiff] = {}

        for pid, new_p in new_players.items():
            old_p = old_players.get(pid)
            if old_p is None:
                # Include all fields for a new player
                pd = TexasHoldemPlayerDiff()
                for fname in TexasHoldemPlayer.model_fields:
                    if fname == "player_id":
                        continue
                    setattr(pd, fname, getattr(new_p, fname))
                players_diff[pid] = pd
            else:
                pd = TexasHoldemPlayerDiff()
                for fname in TexasHoldemPlayer.model_fields:
                    if fname == "player_id":
                        continue
                    ov = getattr(old_p, fname)
                    nv = getattr(new_p, fname)
                    if serialize(ov) != serialize(nv):
                        setattr(pd, fname, nv)
                if pd.model_dump(exclude_none=True):
                    players_diff[pid] = pd

        if players_diff:
            kwargs["players"] = players_diff

        # Generic diff for all other pydantic-declared fields
        for field_name in TexasHoldemState.model_fields:
            if field_name in {"players"}:  # handled above
                continue
            ov = getattr(old, field_name)
            nv = getattr(new, field_name)
            if serialize(ov) != serialize(nv):
                kwargs[field_name] = nv

        return TexasHoldemStateDiff(**kwargs)


class TexasHoldemPlayerDiff(BaseModel):
    """Partial diff for a single `TexasHoldemPlayer`.

    Only fields that changed should be populated. All others remain None (unchanged).
    The player is identified by the key in `TexasHoldemStateDiff.players`.
    """

    chips: int | None = None
    position: int | None = None
    status: PlayerStatus | None = None
    hole_cards: list[Card] | None = None
    current_bet: int | None = None
    total_bet: int | None = None

    def to_dict(self) -> dict[str, Any]:
        return self.model_dump(exclude_none=True)


class TexasHoldemStateDiff(BaseModel):
    """Partial diff for `TexasHoldemState`.

    Only include fields that changed compared to previous state.
    - `players`: map of PlayerId -> `TexasHoldemPlayerDiff` for players that changed.
    - list fields like `community_cards`, `deck`, `winners`, `side_pots` should be the new full value if they changed at all.
    """

    game_id: str | None = None
    is_finished: bool | None = None
    current_player_id: PlayerId | None = None
    round_number: int | None = None
    players: dict[PlayerId, TexasHoldemPlayerDiff] | None = None
    community_cards: list[Card] | None = None
    pot: int | None = None
    side_pots: list[Any] | None = None
    current_bet: int | None = None
    betting_round: BettingRound | None = None
    dealer_position: int | None = None
    small_blind_position: int | None = None
    big_blind_position: int | None = None
    action_position: int | None = None
    last_raise_amount: int | None = None
    last_raise_position: int | None = None
    deck: list[Card] | None = None
    winners: list[str] | None = None
    winning_hands: dict[str, Any] | None = None

    def to_dict(self) -> dict[str, Any]:
        return self.model_dump(exclude_none=True)

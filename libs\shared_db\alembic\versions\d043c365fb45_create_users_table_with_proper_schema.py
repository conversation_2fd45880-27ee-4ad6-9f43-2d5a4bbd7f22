"""Create users table with proper schema

Revision ID: d043c365fb45
Revises: 6d784437db9c
Create Date: 2025-06-03 08:04:37.897668

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "d043c365fb45"
down_revision = "6d784437db9c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "users",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("username", sa.String(), nullable=True),
        sa.Column("email", sa.String(), nullable=True),
        sa.Column("full_name", sa.String(), nullable=True),
        sa.Column(
            "is_active",
            sa.<PERSON>an(),
            server_default=sa.text("true"),
            nullable=False,
        ),
        sa.Column("role", sa.<PERSON>("ADMIN", "USER", name="userrole"), nullable=False),
        sa.Column("cognito_sub", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_users_cognito_sub"), "users", ["cognito_sub"], unique=True)
    op.create_index(op.f("ix_users_email"), "users", ["email"], unique=True)
    op.create_index(op.f("ix_users_id"), "users", ["id"], unique=False)
    op.create_index(op.f("ix_users_username"), "users", ["username"], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_users_username"), table_name="users")
    op.drop_index(op.f("ix_users_id"), table_name="users")
    op.drop_index(op.f("ix_users_email"), table_name="users")
    op.drop_index(op.f("ix_users_cognito_sub"), table_name="users")
    op.drop_table("users")
    # ### end Alembic commands ###

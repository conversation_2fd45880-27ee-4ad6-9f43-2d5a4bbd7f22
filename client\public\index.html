<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent League</title>
</head>
<body>
    <div id="root">
        <!-- Loading fallback content -->
        <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; background-color: #f9fafb;">
            <div style="text-align: center;">
                <div style="width: 32px; height: 32px; border: 2px solid #e5e7eb; border-top: 2px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
                <p style="color: #6b7280; font-family: system-ui, -apple-system, sans-serif;">Loading...</p>
            </div>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    </div>
    <script type="module" src="/src/main.tsx"></script>
</body>
</html>
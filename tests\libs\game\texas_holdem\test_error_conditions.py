"""Tests for error conditions and invalid moves in Texas Hold'em."""

import pytest

from libs.game.texas_holdem.poker import PLAYER_1, PLAYER_2, PLAYER_3, BettingRound, Card, CardRank, CardSuit, PlayerStatus, TexasHoldemAction, TexasHoldemMove
from libs.game.texas_holdem.texas_holdem_errors import TexasHoldemErrors as THErrors

from .test_helpers import PokerTest, TexasHoldemPlayerDiff, TexasHoldemStateDiff


class TestErrorConditions:
    """Test error conditions and invalid move validation."""
    def test_invalid_player_id(self) -> None:
        """Test move with non-existent player ID - this is caught at Pydantic validation level."""
        # This test validates that invalid player IDs are caught by Pydantic validation
        # before reaching the game logic
        with pytest.raises(ValueError):
            _ = TexasHoldemMove(
                player_id="invalid_player",  # type: ignore
                action=TexasHoldemAction.CALL,
                amount=None,
            )

    def test_out_of_turn_move(self) -> None:
        """Test move when it's not the player's turn."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Try to make player_2 act when it's player_1's turn
        error = test.process_move_error(PLAYER_2, TexasHoldemAction.CALL)
        assert error.details.code == THErrors.NOT_PLAYER_TURN.code

    def test_folded_player_cannot_act(self) -> None:
        """Test that folded players cannot make moves."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Player 1 folds
        test.process_move(PLAYER_1, TexasHoldemAction.FOLD)

        # Try to make folded player act again
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error.details.code == THErrors.PLAYER_NOT_ACTIVE.code

    def test_all_in_player_cannot_act(self) -> None:
        """Test that all-in players cannot make further moves."""
        test = PokerTest.create(chips={PLAYER_1: 5})

        # Player 1 goes all-in with insufficient chips to call
        test.process_move(PLAYER_1, TexasHoldemAction.CALL)
        test.assert_state_change(
            TexasHoldemStateDiff(
                players={
                    PLAYER_1: TexasHoldemPlayerDiff(
                        chips=0, status=PlayerStatus.ALL_IN, current_bet=5, total_bet=5
                    )
                },
                pot=20,
                current_player_id=PLAYER_2,
                action_position=1,
            )
        )

        # Try to make all-in player act again (should fail)
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CHECK)
        assert error.details.code == THErrors.PLAYER_NOT_ACTIVE.code

    def test_insufficient_chips_for_call(self) -> None:
        """Test call with insufficient chips goes all-in."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Player 1 makes a big raise
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=50)
        test.assert_state_change(
            TexasHoldemStateDiff(
                players={
                    PLAYER_1: TexasHoldemPlayerDiff(
                        current_bet=50, total_bet=50, chips=950
                    )
                },
                current_bet=50,
                current_player_id=PLAYER_2,
                action_position=1,
                last_raise_amount=40,
                last_raise_position=0,
                pot=65,
            )
        )

        # Give player 2 insufficient chips
        test.state.players[1].chips = 5

        # Player 2 calls with insufficient chips (should go all-in)
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)
        test.assert_state_change(
            TexasHoldemStateDiff(
                players={
                    PLAYER_2: TexasHoldemPlayerDiff(
                        chips=0,
                        status=PlayerStatus.ALL_IN,
                        current_bet=15,
                        total_bet=15,
                    )
                },
                current_player_id=PLAYER_3,
                action_position=2,
                pot=70,
            )
        )

    def test_insufficient_chips_for_raise(self) -> None:
        """Test raise with insufficient chips goes all-in."""
        test = PokerTest.create(current_player_id=PLAYER_1, chips={PLAYER_1: 30})

        # Raise more than available chips (should go all-in)
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=50)
        test.assert_state_change(
            TexasHoldemStateDiff(
                players={
                    PLAYER_1: TexasHoldemPlayerDiff(
                        chips=0,
                        status=PlayerStatus.ALL_IN,
                        current_bet=30,
                        total_bet=30,
                    )
                },
                current_bet=30,
                current_player_id=PLAYER_2,
                action_position=1,
                last_raise_amount=20,
                last_raise_position=0,
                pot=45,
            )
        )

    def test_invalid_raise_amount_too_small(self) -> None:
        """Test raise amount that's too small."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Try to raise by less than minimum (less than 2x big blind)
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.RAISE, amount=15)
        assert error.details.code == THErrors.RAISE_TOO_SMALL.code

    def test_invalid_raise_amount_negative(self) -> None:
        """Test raise with negative amount."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Negative amounts should be caught by pydantic validation
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.RAISE, amount=-10)
        assert error.details.code == THErrors.VALIDATION_ERROR.code

    def test_invalid_raise_amount_zero(self) -> None:
        """Test raise with zero amount."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Zero amount should be handled by game logic
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.RAISE, amount=0)
        assert error.details.code == THErrors.RAISE_TOO_SMALL.code

    def test_check_when_bet_to_call(self) -> None:
        """Test checking when there's a bet to call."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Player 1 raises
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=30)

        # Player 2 tries to check (should fail)
        error = test.process_move_error(PLAYER_2, TexasHoldemAction.CHECK)
        assert error.details.code == THErrors.CANNOT_CHECK.code

    def test_call_when_no_bet(self) -> None:
        """Test calling when there's no bet to call."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Player 1 tries to call when there's no bet
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error.details.code == THErrors.NO_BET_TO_CALL.code

    def test_move_after_game_over(self) -> None:
        """Test making a move after the game is over."""
        test = PokerTest.create(num_players=2, current_player_id=PLAYER_1)

        # Force game to end by making all but one player fold
        test.process_move(PLAYER_1, TexasHoldemAction.FOLD)

        # Try to make a move after game is over
        error = test.process_move_error(PLAYER_2, TexasHoldemAction.CHECK)
        assert error.details.code == THErrors.GAME_OVER.code

    def test_invalid_action_type(self) -> None:
        """Test invalid action type - this is caught at Pydantic validation level."""
        # This test validates that invalid actions are caught by Pydantic validation
        # before reaching the game logic
        with pytest.raises(ValueError):
            _ = TexasHoldemMove(
                player_id=PLAYER_1,
                action="invalid_action",  # type: ignore
                amount=None,
            )

    def test_raise_amount_required(self) -> None:
        """Test raise action without amount specified."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Try to raise without amount
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.RAISE)
        assert error.details.code == THErrors.MISSING_AMOUNT.code

    def test_all_in_with_zero_chips(self) -> None:
        """Test all-in when player has no chips."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Set player chips to 0
        test.state.players[0].chips = 0

        # Try to go all-in with no chips
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.ALL_IN)
        assert error.details.code == THErrors.NO_CHIPS.code

    def test_double_big_blind_rule_violation(self) -> None:
        """Test raise that violates minimum raise rules."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Player 1 makes minimum raise
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=20)

        # Player 2 tries to raise by less than the previous raise amount
        error = test.process_move_error(
            PLAYER_2, TexasHoldemAction.RAISE, amount=25
        )  # Only 5 more
        assert error.details.code == THErrors.RAISE_TOO_SMALL.code

    def test_betting_round_validation(self) -> None:
        """Test that moves are validated against current betting round."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Manually set invalid betting round
        original_round = test.state.betting_round
        test.state.betting_round = BettingRound.SHOWDOWN

        # Try to make a move during showdown
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error.details.code == THErrors.GAME_OVER.code

        # Restore original round
        test.state.betting_round = original_round

    def test_player_status_validation(self) -> None:
        """Test that player status is properly validated."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Manually set player as folded
        test.state.players[0].status = PlayerStatus.FOLDED

        # Try to make a move with folded player
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error.details.code == THErrors.PLAYER_NOT_ACTIVE.code

    def test_chip_consistency_validation(self) -> None:
        """Test that chip amounts remain consistent."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Make some moves
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=30)
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)

    def test_negative_chips_prevention(self) -> None:
        """Test that players cannot have negative chips."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Set player to have exactly enough for big blind
        test.state.players[2].chips = 10  # Exactly the big blind amount
        test.state.players[2].total_bet = 10  # Already posted big blind

        # Player 1 raises
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=30)

        # Player 2 calls
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)

        # Player 3 (big blind) calls with remaining chips (goes all-in)
        test.process_move(PLAYER_3, TexasHoldemAction.CALL)

    def test_action_position_validation(self) -> None:
        """Test that action position is properly validated."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Manually set invalid action position
        test.state.action_position = 999

        # Try to make a move - should handle gracefully
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error is not None

    def test_community_cards_validation(self) -> None:
        """Test validation of community cards state."""
        test = PokerTest.create(current_player_id=PLAYER_1)

        # Manually add too many community cards

        test.state.community_cards = [
            Card(rank=CardRank.TWO, suit=CardSuit.HEARTS),
            Card(rank=CardRank.THREE, suit=CardSuit.HEARTS),
            Card(rank=CardRank.FOUR, suit=CardSuit.HEARTS),
            Card(rank=CardRank.FIVE, suit=CardSuit.HEARTS),
            Card(rank=CardRank.SIX, suit=CardSuit.HEARTS),
            Card(rank=CardRank.SEVEN, suit=CardSuit.HEARTS),  # Too many!
        ]

        # Attempt to make a move with invalid community cards should fail
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error is not None

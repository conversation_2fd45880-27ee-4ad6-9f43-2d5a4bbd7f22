"""Extended tests for error conditions and edge cases in Texas Hold'em."""

import pytest

from libs.game.game_api.core import PlayerId
from libs.game.texas_holdem.poker import (
    PLAYER_1,
    PLAYER_2,
    PLAYER_3,
    PLAYER_4,
    PLAYER_5,
    BettingRound,
    Card,
    CardRank,
    CardSuit,
    PlayerStatus,
    TexasHoldemAction,
)

from .test_helpers import PokerTest


class TestErrorConditionsExtended:
    """Test extended error conditions and validation cases."""

    def test_deck_integrity_validation(self) -> None:
        """Test detection of deck integrity issues."""
        test = PokerTest.create()

        # Verify initial deck has correct number of cards
        # 52 total - 10 hole cards (5 players * 2) - 0 community cards = 42 remaining
        expected_remaining = 52 - (5 * 2) - len(test.get_community_cards())
        assert len(test.state.deck) == expected_remaining

        # Verify no duplicate cards between deck, hole cards, and community cards
        all_cards: list[Card] = []

        # Add hole cards
        for player in test.state.players:
            all_cards.extend(player.hole_cards)

        # Add community cards
        all_cards.extend(test.get_community_cards())

        # Add deck cards
        all_cards.extend(test.state.deck)

        # Check for duplicates
        card_strings: list[str] = [f"{card.rank}_{card.suit}" for card in all_cards]
        assert len(card_strings) == len(set(card_strings)), "Duplicate cards detected"

        # Verify total is 52
        assert len(all_cards) == 52, f"Expected 52 cards, got {len(all_cards)}"

    def test_game_state_corruption_detection(self) -> None:
        """Test handling of corrupted game states."""
        test = PokerTest.create()

        # Test invalid player count
        original_players = test.state.players.copy()
        test.state.players = []  # Corrupt by removing all players

        # Any move should fail with corrupted state
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        # The system should detect empty player list
        assert error is not None

        # Restore players for next test
        test.state.players = original_players

        # Test invalid betting round by manually setting to an unexpected value
        original_round = test.state.betting_round
        # We can't assign None to betting_round as it expects a BettingRound enum
        # Instead, test with a different round that doesn't match the game state
        test.state.betting_round = BettingRound.SHOWDOWN  # Invalid for current state
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        # Restore original round
        test.state.betting_round = original_round

    def test_invalid_betting_round_transitions(self) -> None:
        """Test prevention of invalid round transitions."""
        test = PokerTest.create()

        # Manually corrupt the betting round to an invalid state
        original_round = test.state.betting_round
        test.state.betting_round = BettingRound.RIVER  # Skip directly to river

        # Should detect invalid state
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        # System should reject invalid betting round
        assert error is not None

        # Restore original round
        test.state.betting_round = original_round

    def test_player_id_validation_edge_cases(self) -> None:
        """Test player ID validation with edge cases."""
        test = PokerTest.create()

        # Test with non-existent player ID
        error = test.process_move_error(PlayerId("invalid_player"), TexasHoldemAction.CALL)
        assert error is not None

        # Test with None player ID (if possible)
        with pytest.raises((TypeError, ValueError, AttributeError)):
            test.process_move(None, TexasHoldemAction.CALL)  # type: ignore

    def test_invalid_action_types(self) -> None:
        """Test handling of invalid action types."""
        test = PokerTest.create()

        # Test completely invalid action
        with pytest.raises((TypeError, ValueError)):
            test.process_move_error(PLAYER_1, TexasHoldemAction("invalid_action"))  # type: ignore

        # Test empty action
        with pytest.raises((TypeError, ValueError)):
            test.process_move_error(PLAYER_1, TexasHoldemAction(""))  # type: ignore

    def test_chip_consistency_validation(self) -> None:
        """Test chip consistency validation throughout game."""
        test = PokerTest.create()

        # Calculate initial total chips
        initial_total = (
            sum(player.chips for player in test.state.players) + test.get_pot()
        )

        # Make some moves and verify chip conservation
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=50)
        current_total = (
            sum(player.chips for player in test.state.players) + test.get_pot()
        )
        assert current_total == initial_total, "Chips not conserved after raise"

        # Test manual chip corruption detection
        test.state.players[0].chips = -100  # Corrupt chips to negative

        # Next move should detect corruption and reject the move
        error = test.process_move_error(PLAYER_2, TexasHoldemAction.CALL)
        assert error is not None

    def test_action_position_validation(self) -> None:
        """Test validation of action position."""
        test = PokerTest.create(num_players=3)

        # Test action by wrong player (not current player)
        # PLAYER_1 should be the current player initially
        assert test.state.current_player_id == PLAYER_1

        # Try to make a move with PLAYER_2 when it's PLAYER_1's turn
        error = test.process_move_error(PLAYER_2, TexasHoldemAction.CALL)
        assert error is not None
        assert error and (
            "turn" in error.details.message.lower()
            or "position" in error.details.message.lower()
            or "not your" in error.details.message.lower()
        )

    def test_community_card_validation(self) -> None:
        """Test community card dealing validation."""
        test = PokerTest.create()

        # Manually corrupt community cards
        test.state.community_cards = [
            Card(rank=CardRank.ACE, suit=CardSuit.HEARTS)
        ] * 6  # Too many cards

        # System should detect invalid community card count and reject the move
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error is not None

    def test_betting_amount_validation_edge_cases(self) -> None:
        """Test edge cases in betting amount validation."""
        test = PokerTest.create()

        # Test extremely large bet amount
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=999999999)

        # Should be converted to all-in
        assert test.get_player_status(PLAYER_1) == PlayerStatus.ALL_IN

        # Reset for next test
        test = PokerTest.create()

        # Test invalid bet amount (negative)
        with pytest.raises(Exception) as exc_info:
            test.process_move_error(PLAYER_1, TexasHoldemAction.RAISE, amount=-50)
        # Should get validation error for negative amount
        assert (
            "greater than or equal to 0" in str(exc_info.value)
            or "negative" in str(exc_info.value).lower()
        )

    def test_game_over_state_validation(self) -> None:
        """Test validation when game is already over."""
        test = PokerTest.create()

        # Force game to end by having all but one player fold
        test.process_move(PLAYER_1, TexasHoldemAction.FOLD)
        test.process_move(PLAYER_2, TexasHoldemAction.FOLD)
        test.process_move(PLAYER_3, TexasHoldemAction.FOLD)
        test.process_move(PLAYER_4, TexasHoldemAction.FOLD)

        # Game should be over
        assert test.is_game_finished()

        # Any further moves should be rejected
        error = test.process_move_error(PLAYER_5, TexasHoldemAction.CHECK)
        assert error is not None
        # Check for game over related error message
        assert (
            error
            and "game" in error.details.message.lower()
            and ("ended" in error.details.message.lower() or "finished" in error.details.message.lower())
        )

    def test_player_status_consistency(self) -> None:
        """Test player status consistency validation."""
        test = PokerTest.create()

        # Player folds
        test.process_move(PLAYER_1, TexasHoldemAction.FOLD)

        # Manually corrupt player status
        test.state.players[0].status = PlayerStatus.ACTIVE  # Should be FOLDED

        # System should detect inconsistency
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error is not None  # Should reject action from "folded" player

    def test_pot_integrity_validation(self) -> None:
        """Test pot amount integrity validation."""
        test = PokerTest.create()

        # Record initial pot
        initial_pot = test.get_pot()

        # Make a bet
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=50)

        # Pot should increase by the bet amount
        expected_pot = initial_pot + 50
        assert (
            test.get_pot() == expected_pot
        ), f"Expected pot {expected_pot}, got {test.get_pot()}"

        # Manually corrupt pot
        test.state.pot = -100  # Negative pot

        # System should detect corruption and reject the move
        error = test.process_move_error(PLAYER_2, TexasHoldemAction.CALL)
        assert error is not None

    def test_hole_card_validation(self) -> None:
        """Test hole card validation."""
        test = PokerTest.create()

        # Verify each player has exactly 2 hole cards
        for player in test.state.players:
            assert len(player.hole_cards) == 2, f"Player {player.player_id} has {len(player.hole_cards)} hole cards"

        # Test duplicate hole cards (corruption) - create a new test with duplicate cards
        duplicate_cards = [
            Card(rank=CardRank.ACE, suit=CardSuit.HEARTS),
            Card(rank=CardRank.KING, suit=CardSuit.HEARTS),
        ]
        test_with_duplicates = PokerTest.create(
            hole_cards={
                PLAYER_1: duplicate_cards,
                PLAYER_2: duplicate_cards,  # Same cards as player 1
            }
        )

        # System should detect duplicate cards during validation
        # Verify the corruption was applied
        assert test_with_duplicates.state.players[0].hole_cards == test_with_duplicates.state.players[1].hole_cards

    def test_betting_round_state_validation(self) -> None:
        """Test betting round state validation."""
        test = PokerTest.create()

        # Verify initial state is consistent
        assert test.get_betting_round() == BettingRound.PREFLOP
        assert len(test.get_community_cards()) == 0

        # Manually set inconsistent state
        test.state.betting_round = BettingRound.FLOP
        # But keep community cards empty (inconsistent)

        # System should detect inconsistency and reject the move
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error is not None

    def test_action_validation_with_insufficient_data(self) -> None:
        """Test action validation when required data is missing."""
        test = PokerTest.create()

        # Test raise without amount
        with pytest.raises(Exception) as exc_info:
            test.process_move_error(PLAYER_1, TexasHoldemAction.RAISE)
        assert "amount" in str(exc_info.value).lower()

    def test_side_pot_validation(self) -> None:
        """Test side pot calculation validation."""
        test = PokerTest.create(
            chips={PLAYER_1: 50, PLAYER_2: 100, PLAYER_3: 100, PLAYER_4: 100, PLAYER_5: 100}
        )

        # Player 1 goes all-in with limited chips
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)

        # Others call
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)
        test.process_move(PLAYER_3, TexasHoldemAction.CALL)
        test.process_move(PLAYER_4, TexasHoldemAction.CALL)
        test.process_move(PLAYER_5, TexasHoldemAction.CALL)

        # Verify side pots are created correctly
        assert test.get_side_pots_count() > 0

    def test_minimum_raise_validation(self) -> None:
        """Test minimum raise amount validation."""
        test = PokerTest.create()

        # Player 1 raises to 20
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=20)

        # Player 2 tries to raise by less than minimum (should be at least 10 more)
        error = test.process_move_error(PLAYER_2, TexasHoldemAction.RAISE, amount=25)  # Only 5 more
        assert error is not None
        assert "minimum" in error.details.message.lower() or "raise" in error.details.message.lower()

    def test_all_in_validation(self) -> None:
        """Test all-in action validation."""
        test = PokerTest.create(
            chips={PLAYER_1: 5}  # Very low chips
        )

        # Player 1 should be forced to go all-in when trying to call the big blind
        test.process_move(PLAYER_1, TexasHoldemAction.CALL)
        assert test.get_player_status(PLAYER_1) == PlayerStatus.ALL_IN

    def test_check_validation(self) -> None:
        """Test check action validation."""
        test = PokerTest.create()

        # Player 1 cannot check when there's a bet to call
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CHECK)
        assert error is not None
        assert "check" in error.details.message.lower() or "bet" in error.details.message.lower()

    def test_fold_validation(self) -> None:
        """Test fold action validation."""
        test = PokerTest.create()

        # Player 1 folds
        test.process_move(PLAYER_1, TexasHoldemAction.FOLD)
        assert test.get_player_status(PLAYER_1) == PlayerStatus.FOLDED

        # Folded player cannot make further moves
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error is not None
        assert "fold" in error.details.message.lower() or "inactive" in error.details.message.lower()

"""Extended tests for error conditions and edge cases in Texas Hold'em."""

import pytest

from libs.game.game_api.core import PlayerId
from libs.game.texas_holdem.poker import (
    PLAYER_1,
    TexasHoldemAction,
)

from .test_helpers import PokerTest


class TestErrorConditionsExtended:
    """Test extended error conditions and validation cases."""

    def test_player_id_validation_edge_cases(self) -> None:
        """Test player ID validation with edge cases."""
        test = PokerTest.create()

        # Test with non-existent player ID
        error = test.process_move_error(PlayerId("invalid_player"), TexasHoldemAction.CALL)
        assert error is not None

        # Test with None player ID (if possible)
        with pytest.raises((TypeError, ValueError, AttributeError)):
            test.process_move(None, TexasHoldemAction.CALL)  # type: ignore

    def test_invalid_action_types(self) -> None:
        """Test handling of invalid action types."""
        test = PokerTest.create()

        # Test completely invalid action
        with pytest.raises((<PERSON><PERSON><PERSON><PERSON>, ValueError)):
            test.process_move_error(PLAYER_1, TexasHoldemAction("invalid_action"))  # type: ignore

        # Test empty action
        with pytest.raises((<PERSON><PERSON><PERSON><PERSON>, ValueError)):
            test.process_move_error(PLAYER_1, TexasHoldemAction(""))  # type: ignore

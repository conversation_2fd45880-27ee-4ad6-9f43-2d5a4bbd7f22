#!/usr/bin/env python3
"""Script to convert Card.of usages to new string format."""

import re
import os

def convert_card_usage(content):
    """Convert Card.of usages to new string format."""
    
    # Mapping for CardRank enum values
    rank_mapping = {
        'CardRank.TWO': '2',
        'CardRank.THREE': '3', 
        'CardRank.FOUR': '4',
        'CardRank.FIVE': '5',
        'CardRank.SIX': '6',
        'CardRank.SEVEN': '7',
        'CardRank.EIGHT': '8',
        'CardRank.NINE': '9',
        'CardRank.TEN': '10',
        'CardRank.JACK': 'J',
        'CardRank.QUEEN': 'Q',
        'CardRank.KING': 'K',
        'CardRank.ACE': 'A'
    }
    
    # Mapping for CardSuit enum values
    suit_mapping = {
        'CardSuit.HEARTS': 'h',
        'CardSuit.DIAMONDS': 'd',
        'CardSuit.CLUBS': 'c',
        'CardSuit.SPADES': 's'
    }
    
    # Convert CardRank.X, CardSuit.Y format
    def replace_enum_format(match):
        rank = match.group(1)
        suit = match.group(2)
        rank_str = rank_mapping.get(rank, rank)
        suit_str = suit_mapping.get(suit, suit)
        return f'Card.of("{rank_str}{suit_str}")'
    
    # Convert numeric rank, CardSuit.Y format
    def replace_numeric_format(match):
        rank = match.group(1)
        suit = match.group(2)
        suit_str = suit_mapping.get(suit, suit)
        return f'Card.of("{rank}{suit_str}")'
    
    # Pattern for CardRank.X, CardSuit.Y
    pattern1 = r'Card\.of\((CardRank\.[A-Z]+),\s*(CardSuit\.[A-Z]+)\)'
    content = re.sub(pattern1, replace_enum_format, content)
    
    # Pattern for numeric, CardSuit.Y
    pattern2 = r'Card\.of\((\d+),\s*(CardSuit\.[A-Z]+)\)'
    content = re.sub(pattern2, replace_numeric_format, content)
    
    return content

def process_file(filepath):
    """Process a single file."""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    new_content = convert_card_usage(content)
    
    if new_content != content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"Updated {filepath}")
    else:
        print(f"No changes needed for {filepath}")

def main():
    """Main function."""
    test_dir = 'tests/libs/game/texas_holdem'
    
    for filename in os.listdir(test_dir):
        if filename.endswith('.py'):
            filepath = os.path.join(test_dir, filename)
            process_file(filepath)

if __name__ == '__main__':
    main()
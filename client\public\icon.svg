<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Standalone App Icon - Replace with your actual icon -->
  <defs>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1D4ED8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#iconGradient)" filter="url(#shadow)"/>
  
  <!-- Inner design -->
  <circle cx="32" cy="32" r="18" fill="white" opacity="0.9"/>
  <circle cx="32" cy="32" r="12" fill="url(#iconGradient)"/>
  <circle cx="32" cy="32" r="6" fill="white"/>
  
  <!-- Accent elements -->
  <circle cx="32" cy="20" r="2" fill="url(#iconGradient)"/>
  <circle cx="44" cy="32" r="2" fill="url(#iconGradient)"/>
  <circle cx="32" cy="44" r="2" fill="url(#iconGradient)"/>
  <circle cx="20" cy="32" r="2" fill="url(#iconGradient)"/>
  
  <!-- Corner accents -->
  <circle cx="24" cy="24" r="1.5" fill="white" opacity="0.8"/>
  <circle cx="40" cy="24" r="1.5" fill="white" opacity="0.8"/>
  <circle cx="40" cy="40" r="1.5" fill="white" opacity="0.8"/>
  <circle cx="24" cy="40" r="1.5" fill="white" opacity="0.8"/>
</svg>

"""Mock JWT utilities for testing purposes.
Provides token validation without requiring real AWS Cognito.
"""

import logging
from typing import Any

from shared_db.schemas.auth import TokenData

logger = logging.getLogger(__name__)


class MockCognitoJWTValidator:
    """Mock JWT validator for testing.
    Validates mock tokens generated by MockCognitoService.
    """

    def __init__(self) -> None:
        self._valid_tokens: dict[str, dict[str, Any]] = {}
        logger.info("Initialized Mock Cognito JWT Validator")

    def validate_token(self, token: str) -> TokenData:
        """Validate a mock token and return TokenData.
        Handles both registered tokens and default mock tokens.
        """
        try:
            # Check if token is in our registered tokens
            if token in self._valid_tokens:
                token_data = self._valid_tokens[token]
                return TokenData(
                    username=token_data.get("username"),
                    user_sub=token_data.get("user_sub"),
                    email=token_data.get("email"),
                )

            # Handle default mock tokens for testing
            if token.startswith("mock-"):
                if "admin" in token.lower():
                    return TokenData(
                        username="<EMAIL>",
                        user_sub="mock-user-admin123",
                        email="<EMAIL>",
                    )
                elif token == "mock-user-token":
                    return TokenData(
                        username="<EMAIL>",
                        user_sub="mock-user-test456",
                        email="<EMAIL>",
                    )
                else:
                    # Extract user info from token format: mock-access-{user_sub}-{timestamp}
                    parts = token.split("-")
                    if len(parts) >= 3:
                        user_sub = "-".join(
                            parts[2:-1],
                        )  # Everything between 'access' and timestamp
                        # Try to find user by user_sub in database
                        from shared_db.db import SessionLocal
                        from shared_db.models.user import User

                        db = SessionLocal()
                        try:
                            user = db.query(User).filter(User.cognito_sub == user_sub).first()
                            if user:
                                return TokenData(
                                    username=str(user.email),
                                    user_sub=user_sub,
                                    email=str(user.email),
                                )
                        finally:
                            db.close()

                    # Fallback for unrecognized mock tokens
                    return TokenData(
                        username="<EMAIL>",
                        user_sub="mock-user-default",
                        email="<EMAIL>",
                    )

            # Token not found
            logger.warning(f"Mock JWT: Invalid token {token[:20]}...")
            raise Exception("Invalid token")

        except Exception as e:
            logger.exception(f"Mock JWT validation failed: {e}")
            raise Exception("Invalid authentication token")

    def add_valid_token(self, token: str, username: str, user_sub: str, email: str) -> None:
        """Add a token to the valid tokens registry"""
        self._valid_tokens[token] = {
            "username": username,
            "user_sub": user_sub,
            "email": email,
        }
        logger.debug(f"Mock JWT: Added valid token for {email}")

    def remove_token(self, token: str) -> None:
        """Remove a token from the valid tokens registry"""
        if token in self._valid_tokens:
            del self._valid_tokens[token]
            logger.debug(f"Mock JWT: Removed token {token[:20]}...")

    def clear_all_tokens(self) -> None:
        """Clear all valid tokens (for testing cleanup)"""
        self._valid_tokens.clear()
        logger.info("Mock JWT: Cleared all valid tokens")

    def is_token_valid(self, token: str) -> bool:
        """Check if a token is valid without raising exceptions"""
        try:
            self.validate_token(token)
            return True
        except:
            return False


# Global instance
mock_jwt_validator = MockCognitoJWTValidator()

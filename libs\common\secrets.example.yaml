# Example secrets file - Copy this to secrets.yaml and fill in your actual secrets
# This file should NOT contain actual secrets, only placeholders
#
# For production environments, consider using AWS Secrets Manager instead.
# Set AWS_SECRETS_MANAGER_SECRET_NAME in your .env.production file to use AWS Secrets Manager.
# The secret in AWS Secrets Manager should contain YAML content with the same structure as this file.
#
# Example AWS Secrets Manager YAML content:
# database:
#   username: "postgres"
#   password: "your_database_password_here"
#   host: "your-rds-endpoint.amazonaws.com"
#   port: 5432
#   name: "mydatabase"
#   url: "postgresql://postgres:<EMAIL>:5432/mydatabase"
#
# security:
#   secret_key: "your_secret_key_here"
#   algorithm: "HS256"
#   access_token_expire_minutes: 30
#
# aws:
#   access_key_id: "your_aws_access_key_id"
#   secret_access_key: "your_aws_secret_access_key"

# Database credentials and connection information
database:
  username: "postgres"
  password: "your_database_password_here"
  # Use host.docker.internal for devcontainer, localhost for local development
  host: "host.docker.internal"
  port: 5432
  name: "mydatabase"
  # Full connection string (optional, will be constructed if not provided)
  # Use host.docker.internal for devcontainer compatibility
  url: "postgresql://postgres:<EMAIL>:5432/mydatabase"

security:
  secret_key: "your_secret_key_here"
  algorithm: "HS256"
  access_token_expire_minutes: 30

# AWS credentials
aws:
  access_key_id: "your_aws_access_key_id"
  secret_access_key: "your_aws_secret_access_key"
  region: "us-east-1"

# Cognito configuration is now handled via environment variables
# See .env.development and .env.production for Cognito settings
# The justfile will create Cognito resources and output the IDs/secrets needed

# Add other secrets as needed

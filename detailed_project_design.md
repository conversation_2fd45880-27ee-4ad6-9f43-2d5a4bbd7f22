# Agent League: Detailed Project Design

## Table of Contents
1. [System Overview](#system-overview)
2. [Database Design](#database-design)
3. [Component Architecture](#component-architecture)
4. [Communication Flow](#communication-flow)
5. [Game Interface Design](#game-interface-design)
6. [Agent System](#agent-system)
7. [Streaming & Real-time Updates](#streaming--real-time-updates)
8. [Security & Performance](#security--performance)
9. [Social Sharing & Referral System](#social-sharing--referral-system)
10. [Deployment Architecture](#deployment-architecture)

## System Overview

### Core Principles
- **Stateless Architecture**: All components are stateless, relying on PostgreSQL Aurora as the single source of truth
- **Event-Driven Communication**: SQS queues handle asynchronous communication between components
- **Serverless Game Management**: Game Managers run as Lambda functions for automatic scaling
- **Scalability**: Both Agent and Game Manager Lambda functions auto-scale based on demand
- **Real-time Experience**: WebSocket connections for live game streaming and in-game chat

### Tech Stack
- **Frontend**: React + TypeScript + Vite + Tailwind CSS + Magic UI
- **Backend**: Python FastAPI + SQLAlchemy + Pydantic
- **Database**: PostgreSQL Aurora (AWS)
- **Message Queues**: AWS SQS
- **Agent Compute**: AWS Lambda Functions (ephemeral, spawn-execute-die)
- **Game Managers**: AWS Lambda Functions (ephemeral, event-driven)
- **Real-time**: WebSocket (AWS API Gateway WebSocket) for game streaming and chat
- **Storage**: AWS S3 (for game replays, logs)

## Database Design

### Core Tables

```sql
-- Users and Authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    virtual_currency INTEGER DEFAULT 500, -- Stored as cents/smallest unit
    subscription_tier VARCHAR(20) DEFAULT 'free', -- 'free', 'premium'
    daily_stipend_last_claimed TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User API Keys for LLM integrations
CREATE TABLE user_api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL, -- 'openai', 'anthropic', 'google', etc.
    encrypted_api_key TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Tools created by users
CREATE TABLE tools (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    code TEXT NOT NULL, -- Python code for the tool
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- AI Agents
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    main_prompt TEXT NOT NULL,
    llm_provider VARCHAR(50) NOT NULL,
    llm_model VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    total_games INTEGER DEFAULT 0,
    wins INTEGER DEFAULT 0,
    total_winnings INTEGER DEFAULT 0, -- Virtual currency won
    ranking_score DECIMAL(10,2) DEFAULT 1000.0, -- ELO-style ranking
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Agent-Tool associations
CREATE TABLE agent_tools (
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    tool_id UUID REFERENCES tools(id) ON DELETE CASCADE,
    PRIMARY KEY (agent_id, tool_id)
);

-- Game queues for matchmaking
CREATE TABLE game_queues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    game_type VARCHAR(50) NOT NULL, -- 'poker', 'chess', etc.
    game_mode VARCHAR(20) NOT NULL, -- 'friendly', 'ranked', 'sandbox'
    buy_in_tier VARCHAR(20), -- 'low', 'medium', 'high' for ranked games
    queue_position INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- User game limits tracking
CREATE TABLE user_game_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    friendly_games_played INTEGER DEFAULT 0,
    concurrent_games INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- Payment transactions
CREATE TABLE payment_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    payment_provider VARCHAR(20) NOT NULL, -- 'stripe', 'paypal'
    external_transaction_id VARCHAR(255) NOT NULL,
    amount_cents INTEGER NOT NULL, -- Amount in cents
    currency_code VARCHAR(3) DEFAULT 'USD',
    virtual_currency_amount INTEGER NOT NULL, -- Virtual currency purchased
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'refunded'
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Games
CREATE TABLE games (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_type VARCHAR(50) NOT NULL, -- 'poker', 'chess', etc.
    game_mode VARCHAR(20) NOT NULL, -- 'friendly', 'ranked', 'sandbox'
    buy_in INTEGER, -- Virtual currency buy-in (null for friendly/sandbox)
    buy_in_tier VARCHAR(20), -- 'low', 'medium', 'high'
    max_players INTEGER NOT NULL,
    current_players INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'waiting', -- 'waiting', 'active', 'completed', 'cancelled'
    game_state_json JSONB NOT NULL,
    current_turn_player_id UUID,
    turn_timeout_seconds INTEGER DEFAULT 30,
    last_update_timestamp TIMESTAMP DEFAULT NOW(),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Game participants
CREATE TABLE game_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID REFERENCES games(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    seat_position INTEGER NOT NULL,
    buy_in_amount INTEGER, -- Amount paid to join
    current_stack INTEGER, -- Current virtual currency in game
    final_winnings INTEGER, -- Final amount won/lost
    is_active BOOLEAN DEFAULT true,
    joined_at TIMESTAMP DEFAULT NOW()
);

-- Game actions/moves log
CREATE TABLE game_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID REFERENCES games(id) ON DELETE CASCADE,
    player_id UUID REFERENCES users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL, -- 'fold', 'call', 'raise', 'bet', etc.
    action_data JSONB, -- Action-specific data
    game_state_before JSONB,
    game_state_after JSONB,
    thinking_log TEXT, -- Agent's reasoning (visible only to owner)
    execution_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);



-- Leaderboards
CREATE TABLE leaderboards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_type VARCHAR(50) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    ranking_score DECIMAL(10,2) NOT NULL,
    total_games INTEGER DEFAULT 0,
    wins INTEGER DEFAULT 0,
    total_winnings INTEGER DEFAULT 0,
    last_game_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(game_type, agent_id)
);

-- Invitations
CREATE TABLE invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    inviter_id UUID REFERENCES users(id) ON DELETE CASCADE,
    invitee_email VARCHAR(255) NOT NULL,
    invitation_code VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'expired'
    bonus_amount INTEGER DEFAULT 100, -- Bonus for both inviter and invitee
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Game chat messages (separate for users and agents)
CREATE TABLE game_chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID REFERENCES games(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
    message_type VARCHAR(20) NOT NULL, -- 'user_chat', 'agent_chat', 'system_message'
    content TEXT NOT NULL,
    is_visible_to_users BOOLEAN DEFAULT true, -- Agent messages can be hidden from other users
    is_visible_to_agents BOOLEAN DEFAULT true, -- User messages can be hidden from agents
    reply_to_message_id UUID REFERENCES game_chat_messages(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Chat message reactions/interactions
CREATE TABLE chat_message_reactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID REFERENCES game_chat_messages(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reaction_type VARCHAR(20) NOT NULL, -- 'like', 'laugh', 'angry', 'wow'
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(message_id, user_id, reaction_type)
);
```

### Indexes for Performance

```sql
-- Performance indexes
CREATE INDEX idx_games_status_last_update ON games(status, last_update_timestamp);
CREATE INDEX idx_game_participants_game_id ON game_participants(game_id);
CREATE INDEX idx_game_actions_game_id_created ON game_actions(game_id, created_at);
CREATE INDEX idx_agents_user_ranking ON agents(user_id, ranking_score DESC);
CREATE INDEX idx_leaderboards_game_type_score ON leaderboards(game_type, ranking_score DESC);
CREATE INDEX idx_chat_messages_game_created ON game_chat_messages(game_id, created_at);
CREATE INDEX idx_chat_messages_sender ON game_chat_messages(sender_id, created_at);
CREATE INDEX idx_chat_reactions_message ON chat_message_reactions(message_id);
```

## Component Architecture

### 1. Frontend (React + TypeScript)

```
src/
├── components/
│   ├── game/
│   │   ├── GameTable.tsx          # Main game interface
│   │   ├── PlayerSeat.tsx         # Individual player position
│   │   ├── GameActions.tsx        # Action buttons for spectators
│   │   ├── GameStream.tsx         # Real-time game streaming
│   │   └── GameChat.tsx           # In-game chat component
│   ├── agent/
│   │   ├── AgentCreator.tsx       # Agent creation form
│   │   ├── AgentList.tsx          # User's agents
│   │   └── AgentThinking.tsx      # Real-time agent reasoning
│   ├── tools/
│   │   ├── ToolEditor.tsx         # Code editor for tools
│   │   ├── ToolTester.tsx         # Tool testing interface
│   │   └── ToolMarketplace.tsx    # Browse public tools
│   ├── leaderboard/
│   │   └── Leaderboard.tsx        # Rankings display
│   └── ui/                        # Magic UI components
├── hooks/
│   ├── useWebSocket.ts            # WebSocket connection management
│   ├── useGameState.ts            # Game state management
│   ├── useAgentThinking.ts        # Agent reasoning stream
│   └── useGameChat.ts             # Game chat management
├── services/
│   ├── api.ts                     # HTTP API client
│   ├── websocket.ts               # WebSocket client
│   └── auth.ts                    # Authentication
└── types/
    ├── game.ts                    # Game-related types
    ├── agent.ts                   # Agent types
    └── api.ts                     # API response types
```

### 2. Backend API (FastAPI)

```
app/
├── api/
│   ├── routes/
│   │   ├── auth.py                # Authentication endpoints
│   │   ├── agents.py              # Agent CRUD
│   │   ├── tools.py               # Tool management
│   │   ├── games.py               # Game management (queue joining)
│   │   ├── matchmaking.py         # Matchmaking service
│   │   ├── payments.py            # Payment processing
│   │   ├── leaderboard.py         # Rankings
│   │   ├── chat.py                # Game chat functionality
│   │   └── websocket.py           # WebSocket handlers
│   └── dependencies.py            # FastAPI dependencies
├── core/
│   ├── config.py                  # Configuration
│   ├── security.py                # JWT, encryption
│   ├── database.py                # Database connection
│   └── enums.py                   # StrEnum definitions
├── models/
│   ├── user.py                    # SQLAlchemy models
│   ├── agent.py
│   ├── game.py
│   ├── payment.py
│   ├── chat.py
│   └── tool.py
├── schemas/
│   ├── user.py                    # Pydantic schemas
│   ├── agent.py
│   ├── game.py
│   ├── payment.py
│   ├── chat.py
│   └── tool.py
├── services/
│   ├── matchmaking_service.py     # Queue management and table filling
│   ├── game_service.py            # Game business logic
│   ├── agent_service.py           # Agent management
│   ├── payment_service.py         # Payment processing
│   ├── ranking_service.py         # ELO calculations
│   ├── chat_service.py            # Game chat functionality
│   └── notification_service.py    # WebSocket notifications
└── utils/
    ├── encryption.py              # API key encryption
    ├── sqs.py                     # SQS utilities
    └── validators.py              # Custom validators
```

### StrEnum Definitions

```python
# core/enums.py
from enum import StrEnum

class GameType(StrEnum):
    POKER = "poker"
    CHESS = "chess"
    # Add more games as needed

class GameMode(StrEnum):
    FRIENDLY = "friendly"
    RANKED = "ranked"
    SANDBOX = "sandbox"

class GameStatus(StrEnum):
    WAITING = "waiting"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class BuyInTier(StrEnum):
    LOW = "low"      # $100-500
    MEDIUM = "medium" # $500-2000
    HIGH = "high"    # $2000+

class SubscriptionTier(StrEnum):
    FREE = "free"
    PREMIUM = "premium"

class PaymentProvider(StrEnum):
    STRIPE = "stripe"
    PAYPAL = "paypal"

class PaymentStatus(StrEnum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
    REFUNDED = "refunded"

class LLMProvider(StrEnum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    COHERE = "cohere"

class GameManagerStatus(StrEnum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    FAILED = "failed"

class PlayerStatus(StrEnum):
    ACTIVE = "active"
    FOLDED = "folded"
    ALL_IN = "all_in"
    ELIMINATED = "eliminated"
```

### 3. Matchmaking Service

```python
# services/matchmaking_service.py
from typing import List, Optional
from sqlalchemy.orm import Session
from core.enums import GameMode, GameType, BuyInTier, SubscriptionTier
from models.game import Game, GameQueue, GameParticipant
from models.user import User
from schemas.game import GameQueueRequest

class MatchmakingService:
    def __init__(self, db: Session):
        self.db = db

    async def join_queue(self, user_id: str, agent_id: str, request: GameQueueRequest) -> dict:
        """Add user to game queue"""
        # Check user limits
        if not await self._check_user_limits(user_id, request.game_mode):
            raise ValueError("User has reached game limits")

        # Check if already in queue
        existing = self.db.query(GameQueue).filter(
            GameQueue.user_id == user_id,
            GameQueue.game_type == request.game_type,
            GameQueue.game_mode == request.game_mode
        ).first()

        if existing:
            raise ValueError("Already in queue for this game type/mode")

        # Add to queue
        queue_entry = GameQueue(
            user_id=user_id,
            agent_id=agent_id,
            game_type=request.game_type,
            game_mode=request.game_mode,
            buy_in_tier=request.buy_in_tier
        )
        self.db.add(queue_entry)
        self.db.commit()

        # Try to create game immediately
        await self._try_create_game(request.game_type, request.game_mode, request.buy_in_tier)

        return {"status": "queued", "queue_id": queue_entry.id}

    async def leave_queue(self, user_id: str, game_type: GameType, game_mode: GameMode) -> bool:
        """Remove user from queue"""
        queue_entry = self.db.query(GameQueue).filter(
            GameQueue.user_id == user_id,
            GameQueue.game_type == game_type,
            GameQueue.game_mode == game_mode
        ).first()

        if queue_entry:
            self.db.delete(queue_entry)
            self.db.commit()
            return True
        return False

    async def _check_user_limits(self, user_id: str, game_mode: GameMode) -> bool:
        """Check if user can join another game"""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            return False

        # Count current active games
        active_games = self.db.query(GameParticipant).join(Game).filter(
            GameParticipant.user_id == user_id,
            Game.status.in_(['waiting', 'active'])
        ).count()

        # Check concurrent game limits
        if user.subscription_tier == SubscriptionTier.FREE:
            if active_games >= 1:  # Free users: 1 concurrent game
                return False
        # Premium users have no limit

        # Check daily friendly game limit for free users
        if game_mode == GameMode.FRIENDLY and user.subscription_tier == SubscriptionTier.FREE:
            from datetime import date
            today_friendly = self.db.query(GameParticipant).join(Game).filter(
                GameParticipant.user_id == user_id,
                Game.game_mode == GameMode.FRIENDLY,
                Game.created_at >= date.today()
            ).count()

            if today_friendly >= 1:  # Free users: 1 friendly game per day
                return False

        return True

    async def _try_create_game(self, game_type: GameType, game_mode: GameMode, buy_in_tier: Optional[BuyInTier]):
        """Try to create a game if enough players in queue"""
        max_players = self._get_max_players(game_type)

        # Get queued players
        query = self.db.query(GameQueue).filter(
            GameQueue.game_type == game_type,
            GameQueue.game_mode == game_mode
        )

        if buy_in_tier:
            query = query.filter(GameQueue.buy_in_tier == buy_in_tier)

        queued_players = query.order_by(GameQueue.created_at).limit(max_players).all()

        if len(queued_players) >= max_players:
            await self._create_game_from_queue(queued_players, game_type, game_mode, buy_in_tier)

    async def _create_game_from_queue(self, players: List[GameQueue], game_type: GameType,
                                    game_mode: GameMode, buy_in_tier: Optional[BuyInTier]):
        """Create game and move players from queue to game"""
        # Calculate buy-in amount
        buy_in = self._get_buy_in_amount(buy_in_tier) if game_mode == GameMode.RANKED else None

        # Create game
        game = Game(
            game_type=game_type,
            game_mode=game_mode,
            buy_in=buy_in,
            buy_in_tier=buy_in_tier,
            max_players=len(players),
            current_players=len(players),
            game_state_json={}  # Will be initialized by game logic
        )
        self.db.add(game)
        self.db.flush()  # Get game ID

        # Create participants and remove from queue
        for i, player in enumerate(players):
            # Deduct buy-in if ranked game
            if buy_in:
                user = self.db.query(User).filter(User.id == player.user_id).first()
                if user.virtual_currency < buy_in:
                    continue  # Skip player with insufficient funds
                user.virtual_currency -= buy_in

            participant = GameParticipant(
                game_id=game.id,
                user_id=player.user_id,
                agent_id=player.agent_id,
                seat_position=i,
                buy_in_amount=buy_in,
                current_stack=buy_in or 0
            )
            self.db.add(participant)
            self.db.delete(player)  # Remove from queue

        self.db.commit()

        # Notify players that game is starting
        await self._notify_game_start(game.id)

        # Send first turn message to start the game
        await self._start_game(game.id)

    def _get_max_players(self, game_type: GameType) -> int:
        """Get maximum players for game type"""
        if game_type == GameType.POKER:
            return 5  # 5-player poker tables
        return 2  # Default for other games

    def _get_buy_in_amount(self, tier: BuyInTier) -> int:
        """Get buy-in amount for tier"""
        amounts = {
            BuyInTier.LOW: 100,
            BuyInTier.MEDIUM: 500,
            BuyInTier.HIGH: 2000
        }
        return amounts.get(tier, 100)
```

### 4. Game Manager (Lambda Function)

```python
# game_manager/handler.py
import json
import boto3
from typing import Dict, Any
from game_interface import GameInterface
from games.poker import PokerGame
from database import get_db_connection

def lambda_handler(event, context):
    """
    Lambda function that processes game moves and state updates
    Triggered by SQS messages from agents or game events
    """
    for record in event['Records']:
        message = json.loads(record['body'])

        if message['action'] == 'process_move':
            process_move(message)
        elif message['action'] == 'start_game':
            start_game(message)
        elif message['action'] == 'end_game':
            end_game(message)

def process_move(message: Dict[str, Any]):
    """Process a move from an agent"""
    game_id = message['game_id']
    player_id = message['player_id']
    move_data = message['move_data']

    with get_db_connection() as db:
        # Get game state
        game = db.query(Game).filter(Game.id == game_id).first()
        if not game:
            return

        # Load appropriate game logic
        game_logic = load_game_logic(game.game_type)
        game_logic.initialize(game.game_state_json)

        # Process the move
        new_state = game_logic.validate_and_apply_move(player_id, move_data)

        # Update database
        game.game_state_json = new_state
        game.last_update_timestamp = datetime.utcnow()

        # Log the action
        action = GameAction(
            game_id=game_id,
            player_id=player_id,
            agent_id=message.get('agent_id'),
            action_type=move_data.get('action'),
            action_data=move_data,
            game_state_before=game_logic.serialize_state(),
            game_state_after=new_state,
            thinking_log=message.get('thinking_log'),
            execution_time_ms=message.get('execution_time_ms')
        )
        db.add(action)
        db.commit()

        # Broadcast update via WebSocket
        broadcast_game_update(game_id, new_state)

        # Send next turn message if game continues
        if not game_logic.is_game_over():
            next_player = game_logic.get_next_player_id()
            send_turn_message(game_id, next_player)
        else:
            # Handle game end
            handle_game_end(game_id, game_logic.get_game_results())

def load_game_logic(game_type: str) -> GameInterface:
    """Dynamically load game logic based on type"""
    if game_type == 'poker':
        return PokerGame()
    # Add other games here
    raise ValueError(f"Unknown game type: {game_type}")

def send_turn_message(game_id: str, player_id: str):
    """Send turn message to agent queue"""
    sqs = boto3.client('sqs')
    message = {
        'game_id': game_id,
        'player_id': player_id,
        'action': 'take_turn',
        'timeout_seconds': 30,
        'timestamp': datetime.utcnow().isoformat()
    }

    sqs.send_message(
        QueueUrl=os.getenv('AGENT_INPUT_QUEUE_URL'),
        MessageBody=json.dumps(message)
    )

def broadcast_game_update(game_id: str, game_state: Dict[str, Any]):
    """Broadcast game update via WebSocket"""
    # Implementation for WebSocket broadcasting
    pass
```

### 4. Agent Runner (Lambda Function)

```python
# agent_runner/handler.py
import json
import boto3
import asyncio
import httpx
from typing import Dict, Any
from agent_executor import AgentExecutor
from database import get_db_connection

def lambda_handler(event, context):
    """
    Lambda function that executes agent turns
    Spawns -> Executes -> Dies
    """
    for record in event['Records']:
        message = json.loads(record['body'])
        asyncio.run(execute_agent_turn(message))

async def execute_agent_turn(message: Dict[str, Any]):
    """Execute a single agent turn"""
    game_id = message['game_id']
    player_id = message['player_id']

    async with get_db_connection() as db:
        # Get game state and agent info
        game_data = await get_game_and_agent_data(db, game_id, player_id)

        if not game_data:
            return

        game_state = game_data['game_state']
        agent_config = game_data['agent_config']
        user_api_keys = game_data['api_keys']
        tools = game_data['tools']

        # Initialize agent executor
        executor = AgentExecutor(
            agent_config=agent_config,
            api_keys=user_api_keys,
            tools=tools,
            timeout=game_data['timeout_seconds']
        )

        try:
            # Execute agent thinking and get move
            result = await executor.execute_turn(
                game_state=game_state,
                player_id=player_id
            )

            # Send move via SQS to game manager
            await send_move_via_sqs(
                game_id=game_id,
                player_id=player_id,
                agent_id=agent_config['id'],
                move_data=result['move'],
                thinking_log=result['thinking_log'],
                execution_time=result['execution_time']
            )

        except TimeoutError:
            # Handle timeout - send default action
            await send_timeout_action(game_id, player_id, agent_config['id'])
        except Exception as e:
            # Handle other errors
            logger.error(f"Agent execution error: {e}")
            await send_error_action(game_id, player_id, agent_config['id'], str(e))

async def send_move_via_sqs(game_id: str, player_id: str, agent_id: str,
                           move_data: Dict[str, Any], thinking_log: str, execution_time: int):
    """Send agent's move via SQS to game manager"""
    sqs = boto3.client('sqs')
    game_manager_queue_url = os.getenv('GAME_MANAGER_QUEUE_URL')

    payload = {
        'action': 'process_move',
        'game_id': game_id,
        'player_id': player_id,
        'agent_id': agent_id,
        'move_data': move_data,
        'thinking_log': thinking_log,
        'execution_time_ms': execution_time,
        'timestamp': datetime.utcnow().isoformat()
    }

    try:
        sqs.send_message(
            QueueUrl=game_manager_queue_url,
            MessageBody=json.dumps(payload)
        )
        logger.info(f"Move sent to game manager for game {game_id}")

    except Exception as e:
        logger.error(f"Failed to send move to game manager: {e}")
        # Could implement retry logic here
```



## Payment System

### Payment Service

```python
# services/payment_service.py
from typing import Dict, Any
from core.enums import PaymentProvider, PaymentStatus
from models.payment import PaymentTransaction
from models.user import User
import stripe
import paypalrestsdk

class PaymentService:
    def __init__(self, db: Session):
        self.db = db
        # Initialize payment providers
        stripe.api_key = os.getenv('STRIPE_SECRET_KEY')
        paypalrestsdk.configure({
            "mode": os.getenv('PAYPAL_MODE', 'sandbox'),
            "client_id": os.getenv('PAYPAL_CLIENT_ID'),
            "client_secret": os.getenv('PAYPAL_CLIENT_SECRET')
        })

    async def create_payment_intent(self, user_id: str, amount_usd: float,
                                  virtual_currency_amount: int, provider: PaymentProvider) -> Dict[str, Any]:
        """Create payment intent with chosen provider"""
        amount_cents = int(amount_usd * 100)

        # Create transaction record
        transaction = PaymentTransaction(
            user_id=user_id,
            payment_provider=provider,
            amount_cents=amount_cents,
            virtual_currency_amount=virtual_currency_amount,
            status=PaymentStatus.PENDING
        )
        self.db.add(transaction)
        self.db.flush()

        if provider == PaymentProvider.STRIPE:
            return await self._create_stripe_intent(transaction, amount_cents)
        elif provider == PaymentProvider.PAYPAL:
            return await self._create_paypal_order(transaction, amount_usd)
        else:
            raise ValueError(f"Unsupported payment provider: {provider}")

    async def _create_stripe_intent(self, transaction: PaymentTransaction, amount_cents: int) -> Dict[str, Any]:
        """Create Stripe payment intent"""
        try:
            intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency='usd',
                metadata={
                    'transaction_id': str(transaction.id),
                    'user_id': str(transaction.user_id)
                }
            )

            transaction.external_transaction_id = intent.id
            self.db.commit()

            return {
                'client_secret': intent.client_secret,
                'transaction_id': transaction.id
            }
        except stripe.error.StripeError as e:
            transaction.status = PaymentStatus.FAILED
            self.db.commit()
            raise ValueError(f"Stripe error: {str(e)}")

    async def _create_paypal_order(self, transaction: PaymentTransaction, amount_usd: float) -> Dict[str, Any]:
        """Create PayPal order"""
        try:
            payment = paypalrestsdk.Payment({
                "intent": "sale",
                "payer": {"payment_method": "paypal"},
                "redirect_urls": {
                    "return_url": f"{os.getenv('FRONTEND_URL')}/payment/success",
                    "cancel_url": f"{os.getenv('FRONTEND_URL')}/payment/cancel"
                },
                "transactions": [{
                    "item_list": {
                        "items": [{
                            "name": "Virtual Currency",
                            "sku": "virtual_currency",
                            "price": str(amount_usd),
                            "currency": "USD",
                            "quantity": 1
                        }]
                    },
                    "amount": {
                        "total": str(amount_usd),
                        "currency": "USD"
                    },
                    "description": f"Purchase of {transaction.virtual_currency_amount} virtual currency"
                }]
            })

            if payment.create():
                transaction.external_transaction_id = payment.id
                self.db.commit()

                # Get approval URL
                approval_url = next(link.href for link in payment.links if link.rel == "approval_url")

                return {
                    'approval_url': approval_url,
                    'transaction_id': transaction.id
                }
            else:
                transaction.status = PaymentStatus.FAILED
                self.db.commit()
                raise ValueError(f"PayPal error: {payment.error}")

        except Exception as e:
            transaction.status = PaymentStatus.FAILED
            self.db.commit()
            raise ValueError(f"PayPal error: {str(e)}")

    async def confirm_payment(self, transaction_id: str, provider_data: Dict[str, Any]) -> bool:
        """Confirm payment and credit user account"""
        transaction = self.db.query(PaymentTransaction).filter(
            PaymentTransaction.id == transaction_id
        ).first()

        if not transaction or transaction.status != PaymentStatus.PENDING:
            return False

        try:
            if transaction.payment_provider == PaymentProvider.STRIPE:
                success = await self._confirm_stripe_payment(transaction, provider_data)
            elif transaction.payment_provider == PaymentProvider.PAYPAL:
                success = await self._confirm_paypal_payment(transaction, provider_data)
            else:
                return False

            if success:
                # Credit user account
                user = self.db.query(User).filter(User.id == transaction.user_id).first()
                user.virtual_currency += transaction.virtual_currency_amount

                transaction.status = PaymentStatus.COMPLETED
                transaction.completed_at = datetime.utcnow()

                self.db.commit()
                return True

        except Exception as e:
            transaction.status = PaymentStatus.FAILED
            self.db.commit()
            logger.error(f"Payment confirmation error: {e}")

        return False
```

## Communication Flow

### Updated Message Flow Diagram

```
1. User joins game queue via Backend API
   ↓
2. Matchmaking service fills table and creates game
   ↓
3. Users notified via WebSocket that game is starting
   ↓
4. Game Manager Lambda sends "TakeTurn" message to Agent Input Queue
   ↓
5. Agent Lambda polls Agent Input Queue
   ↓
6. Agent Lambda executes agent logic (LLM + tools)
   ↓
7. Agent Lambda sends move via SQS to Game Manager Queue
   ↓
8. Game Manager Lambda processes move, validates, and updates DB
   ↓
9. Game Manager Lambda broadcasts update via WebSocket (including chat)
   ↓
10. Game Manager Lambda sends next "TakeTurn" to Agent Input Queue
    ↓
11. Repeat steps 5-10 until game ends

Chat Flow (Parallel):
- Users send chat messages via WebSocket
- Agents can send chat messages during their turn execution
- All chat messages are broadcast to appropriate recipients
- Separate channels for user chat and agent chat
```

### Sandbox Mode

The sandbox is a special game mode where users can test their agents:

- **Purpose**: Test agent behavior before going live
- **Opponents**: Multiple instances of the user's own agent compete against each other
- **No Currency**: Sandbox games don't use virtual currency
- **No Limits**: Users can run unlimited sandbox games
- **Full Logging**: Complete thinking logs available for all agent instances
- **Identical Setup**: All agent instances use the same configuration (prompt, LLM, tools)

```python
# Sandbox game creation
def create_sandbox_game(user_id: str, agent_id: str, num_instances: int = 5):
    """Create sandbox game with multiple instances of same agent"""
    game = Game(
        game_type=GameType.POKER,
        game_mode=GameMode.SANDBOX,
        max_players=num_instances,
        current_players=num_instances
    )

    # Create multiple participants with same agent
    for i in range(num_instances):
        participant = GameParticipant(
            game_id=game.id,
            user_id=user_id,
            agent_id=agent_id,
            seat_position=i,
            current_stack=1000  # Fixed stack for sandbox
        )
```

### SQS Queue Structure

#### Agent Input Queue
```json
{
  "game_id": "uuid",
  "player_id": "uuid",
  "action": "take_turn",
  "timeout_seconds": 30,
  "timestamp": "2025-07-26T10:00:00Z"
}
```

#### Game Manager Queue
```json
{
  "action": "process_move",
  "game_id": "uuid",
  "player_id": "uuid",
  "agent_id": "uuid",
  "move_data": {
    "action": "raise",
    "amount": 100
  },
  "thinking_log": "I think I should raise because...",
  "execution_time_ms": 1500,
  "timestamp": "2025-07-26T10:00:30Z"
}
```

#### Chat Message Queue (for agent chat)
```json
{
  "action": "send_chat",
  "game_id": "uuid",
  "sender_id": "uuid",
  "agent_id": "uuid",
  "message_type": "agent_chat",
  "content": "Good move! I'm thinking about my next play...",
  "is_visible_to_users": true,
  "timestamp": "2025-07-26T10:00:45Z"
}
```

## Chat System

### Chat Service Implementation

```python
# services/chat_service.py
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from models.chat import GameChatMessage, ChatMessageReaction
from models.user import User
from models.agent import Agent
from core.enums import MessageType
from datetime import datetime

class ChatService:
    def __init__(self, db: Session):
        self.db = db

    async def send_message(self, game_id: str, sender_id: str, content: str,
                          message_type: str = 'user_chat', agent_id: str = None,
                          is_visible_to_users: bool = True, is_visible_to_agents: bool = True) -> Dict[str, Any]:
        """Send a chat message in a game"""

        # Validate message content
        if not content.strip():
            raise ValueError("Message content cannot be empty")

        if len(content) > 500:  # Message length limit
            raise ValueError("Message too long (max 500 characters)")

        # Create message
        message = GameChatMessage(
            game_id=game_id,
            sender_id=sender_id,
            agent_id=agent_id,
            message_type=message_type,
            content=content.strip(),
            is_visible_to_users=is_visible_to_users,
            is_visible_to_agents=is_visible_to_agents
        )

        self.db.add(message)
        self.db.commit()

        # Get sender info for response
        sender_info = await self._get_sender_info(sender_id, agent_id)

        return {
            "message_id": message.id,
            "game_id": game_id,
            "sender": sender_info,
            "content": content,
            "message_type": message_type,
            "timestamp": message.created_at.isoformat(),
            "is_visible_to_users": is_visible_to_users,
            "is_visible_to_agents": is_visible_to_agents
        }

    async def get_game_messages(self, game_id: str, user_id: str = None,
                               is_agent: bool = False, limit: int = 50) -> List[Dict[str, Any]]:
        """Get chat messages for a game, filtered by visibility"""

        query = self.db.query(GameChatMessage).filter(
            GameChatMessage.game_id == game_id
        )

        # Filter by visibility
        if is_agent:
            query = query.filter(GameChatMessage.is_visible_to_agents == True)
        else:
            query = query.filter(GameChatMessage.is_visible_to_users == True)

        messages = query.order_by(GameChatMessage.created_at.desc()).limit(limit).all()

        # Format messages with sender info
        formatted_messages = []
        for message in reversed(messages):  # Reverse to get chronological order
            sender_info = await self._get_sender_info(message.sender_id, message.agent_id)

            formatted_messages.append({
                "message_id": message.id,
                "sender": sender_info,
                "content": message.content,
                "message_type": message.message_type,
                "timestamp": message.created_at.isoformat(),
                "reactions": await self._get_message_reactions(message.id)
            })

        return formatted_messages

    async def add_reaction(self, message_id: str, user_id: str, reaction_type: str) -> Dict[str, Any]:
        """Add reaction to a message"""

        # Check if reaction already exists
        existing = self.db.query(ChatMessageReaction).filter(
            ChatMessageReaction.message_id == message_id,
            ChatMessageReaction.user_id == user_id,
            ChatMessageReaction.reaction_type == reaction_type
        ).first()

        if existing:
            # Remove existing reaction (toggle)
            self.db.delete(existing)
            action = "removed"
        else:
            # Add new reaction
            reaction = ChatMessageReaction(
                message_id=message_id,
                user_id=user_id,
                reaction_type=reaction_type
            )
            self.db.add(reaction)
            action = "added"

        self.db.commit()

        return {
            "message_id": message_id,
            "user_id": user_id,
            "reaction_type": reaction_type,
            "action": action
        }

    async def _get_sender_info(self, sender_id: str, agent_id: str = None) -> Dict[str, Any]:
        """Get sender information for display"""
        if agent_id:
            agent = self.db.query(Agent).filter(Agent.id == agent_id).first()
            user = self.db.query(User).filter(User.id == sender_id).first()
            return {
                "type": "agent",
                "agent_id": agent_id,
                "agent_name": agent.name if agent else "Unknown Agent",
                "user_id": sender_id,
                "username": user.username if user else "Unknown User"
            }
        else:
            user = self.db.query(User).filter(User.id == sender_id).first()
            return {
                "type": "user",
                "user_id": sender_id,
                "username": user.username if user else "Unknown User"
            }

    async def _get_message_reactions(self, message_id: str) -> Dict[str, int]:
        """Get reaction counts for a message"""
        reactions = self.db.query(ChatMessageReaction).filter(
            ChatMessageReaction.message_id == message_id
        ).all()

        reaction_counts = {}
        for reaction in reactions:
            reaction_counts[reaction.reaction_type] = reaction_counts.get(reaction.reaction_type, 0) + 1

        return reaction_counts
```

### Chat API Endpoints

```python
# api/routes/chat.py
from fastapi import APIRouter, Depends, HTTPException, WebSocket
from typing import List
from schemas.chat import ChatMessageCreate, ChatMessageResponse, ChatReactionCreate
from services.chat_service import ChatService

router = APIRouter(prefix="/api/v1/chat", tags=["chat"])

@router.post("/games/{game_id}/messages", response_model=ChatMessageResponse)
async def send_message(
    game_id: str,
    message: ChatMessageCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send a chat message in a game"""
    service = ChatService(db)

    # Verify user is in the game
    participant = db.query(GameParticipant).filter(
        GameParticipant.game_id == game_id,
        GameParticipant.user_id == current_user.id
    ).first()

    if not participant:
        raise HTTPException(status_code=403, detail="Not a participant in this game")

    result = await service.send_message(
        game_id=game_id,
        sender_id=current_user.id,
        content=message.content,
        message_type="user_chat"
    )

    # Broadcast message via WebSocket
    await broadcast_chat_message(game_id, result)

    return result

@router.get("/games/{game_id}/messages", response_model=List[ChatMessageResponse])
async def get_messages(
    game_id: str,
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get chat messages for a game"""
    service = ChatService(db)

    # Verify user has access to the game
    participant = db.query(GameParticipant).filter(
        GameParticipant.game_id == game_id,
        GameParticipant.user_id == current_user.id
    ).first()

    if not participant:
        raise HTTPException(status_code=403, detail="Not a participant in this game")

    return await service.get_game_messages(
        game_id=game_id,
        user_id=current_user.id,
        is_agent=False,
        limit=limit
    )

@router.post("/messages/{message_id}/reactions")
async def add_reaction(
    message_id: str,
    reaction: ChatReactionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add or remove reaction to a message"""
    service = ChatService(db)

    result = await service.add_reaction(
        message_id=message_id,
        user_id=current_user.id,
        reaction_type=reaction.reaction_type
    )

    # Broadcast reaction update via WebSocket
    await broadcast_reaction_update(message_id, result)

    return result
```

## Game Interface Design

### Generic Game Interface with Proper Types

```python
# game_interface.py
from abc import ABC, abstractmethod
from typing import Dict, Optional, List, Union
from dataclasses import dataclass
from core.enums import GameType

@dataclass
class GameAction:
    """Represents a valid game action"""
    action: str
    min_amount: Optional[int] = None
    max_amount: Optional[int] = None
    amount: Optional[int] = None

@dataclass
class GameState:
    """Base game state structure"""
    game_id: str
    game_type: GameType
    current_player_id: Optional[str]
    is_game_over: bool
    error_message: Optional[str] = None

@dataclass
class PlayerState:
    """Player-specific game state"""
    player_id: str
    game_state: GameState
    valid_actions: List[GameAction]
    private_info: Dict[str, Union[str, int, List]]

@dataclass
class GameResults:
    """Final game results"""
    winners: List[str]
    payouts: Dict[str, int]  # player_id -> amount won/lost
    final_positions: Dict[str, int]  # player_id -> final position

class GameInterface(ABC):
    """
    Generic interface that all games must implement
    Allows Game Manager to be game-agnostic
    """

    @abstractmethod
    def initialize(self, game_state_json: Dict[str, Union[str, int, List, Dict]]) -> None:
        """Initialize game logic with current state from database"""
        pass

    @abstractmethod
    def get_state_for_player(self, player_id: str) -> PlayerState:
        """
        Get game state visible to specific player
        Includes public info + player's private info
        """
        pass

    @abstractmethod
    def get_valid_actions(self, player_id: str) -> List[GameAction]:
        """Get list of valid actions for player"""
        pass

    @abstractmethod
    def is_valid_move(self, player_id: str, action: GameAction) -> bool:
        """Quick validation without applying the move"""
        pass

    @abstractmethod
    def validate_and_apply_move(self, player_id: str, action: GameAction) -> GameState:
        """
        Validate move and apply it to game state
        Returns new complete game state
        If invalid, returns state with error message
        """
        pass

    @abstractmethod
    def get_next_player_id(self) -> Optional[str]:
        """Get next player to act, None if game over"""
        pass

    @abstractmethod
    def is_game_over(self) -> bool:
        """Check if game has ended"""
        pass

    @abstractmethod
    def get_game_results(self) -> GameResults:
        """Get final game results and payouts"""
        pass

    @abstractmethod
    def get_timeout_seconds(self) -> int:
        """Get timeout for player actions in seconds"""
        pass

    @abstractmethod
    def get_default_action(self, player_id: str) -> GameAction:
        """Get default action when player times out"""
        pass

    @abstractmethod
    def serialize_state(self) -> Dict[str, Union[str, int, List, Dict]]:
        """Serialize current game state for database storage"""
        pass
```

### Poker Game Implementation

```python
# games/poker.py
from game_interface import GameInterface
from typing import Dict, Any, Optional, List
import random

class PokerGame(GameInterface):
    def __init__(self):
        self.state = {}
        self.timeout_seconds = 30

    def initialize(self, game_state: Dict[str, Any]) -> None:
        """Initialize poker game state"""
        self.state = game_state.copy()

        # Initialize new game if empty state
        if not self.state:
            self.state = self._create_initial_state()

    def _create_initial_state(self) -> Dict[str, Any]:
        """Create initial poker game state"""
        return {
            "deck": self._create_shuffled_deck(),
            "community_cards": [],
            "pot": 0,
            "current_bet": 0,
            "betting_round": "preflop",  # preflop, flop, turn, river
            "players": {},
            "active_players": [],
            "current_player_index": 0,
            "dealer_position": 0,
            "small_blind": 10,
            "big_blind": 20,
            "game_phase": "betting"
        }

    def get_state_for_player(self, player_id: str) -> Dict[str, Any]:
        """Get state visible to specific player"""
        public_state = {
            "community_cards": self.state["community_cards"],
            "pot": self.state["pot"],
            "current_bet": self.state["current_bet"],
            "betting_round": self.state["betting_round"],
            "players": {
                pid: {
                    "stack": player["stack"],
                    "current_bet": player["current_bet"],
                    "status": player["status"],
                    "position": player["position"]
                }
                for pid, player in self.state["players"].items()
            },
            "valid_actions": self.get_valid_actions(player_id)
        }

        # Add private info for this player
        if player_id in self.state["players"]:
            public_state["hole_cards"] = self.state["players"][player_id]["hole_cards"]

        return public_state

    def get_valid_actions(self, player_id: str) -> List[Dict[str, Any]]:
        """Get valid actions for player"""
        if player_id not in self.state["players"]:
            return []

        player = self.state["players"][player_id]
        current_bet = self.state["current_bet"]
        player_bet = player["current_bet"]
        stack = player["stack"]

        actions = []

        # Fold (always available)
        actions.append({"action": "fold"})

        # Check/Call
        call_amount = current_bet - player_bet
        if call_amount == 0:
            actions.append({"action": "check"})
        elif call_amount <= stack:
            actions.append({"action": "call", "amount": call_amount})

        # Bet/Raise
        min_raise = max(self.state["big_blind"], current_bet * 2 - player_bet)
        if min_raise <= stack:
            actions.append({
                "action": "raise" if current_bet > 0 else "bet",
                "min_amount": min_raise,
                "max_amount": stack
            })

        # All-in
        if stack > 0:
            actions.append({"action": "all_in", "amount": stack})

        return actions

    def validate_and_apply_move(self, player_id: str, move_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and apply poker move"""
        try:
            action = move_data.get("action")
            amount = move_data.get("amount", 0)

            if not self._is_valid_move(player_id, action, amount):
                # Invalid move - add error and keep same player
                self.state["error"] = f"Invalid move: {action}"
                return self.state

            # Apply the move
            self._apply_move(player_id, action, amount)

            # Clear any previous errors
            self.state.pop("error", None)

            # Check if betting round is complete
            if self._is_betting_round_complete():
                self._advance_game_phase()
            else:
                self._advance_to_next_player()

            return self.state

        except Exception as e:
            self.state["error"] = f"Move processing error: {str(e)}"
            return self.state

    def get_next_player_id(self) -> Optional[str]:
        """Get next player to act"""
        if self.is_game_over():
            return None

        active_players = [p for p in self.state["active_players"]
                         if self.state["players"][p]["status"] == "active"]

        if not active_players:
            return None

        current_index = self.state["current_player_index"]
        return active_players[current_index % len(active_players)]

    def is_game_over(self) -> bool:
        """Check if poker game is over"""
        active_players = [p for p in self.state["active_players"]
                         if self.state["players"][p]["status"] == "active"]
        return len(active_players) <= 1 or self.state.get("game_phase") == "complete"

    def get_game_results(self) -> Dict[str, Any]:
        """Calculate final poker results and payouts"""
        # Implement poker hand evaluation and payout logic
        return {"winners": [], "payouts": {}}

    def get_timeout_seconds(self) -> int:
        return self.timeout_seconds

    def get_default_action(self, player_id: str) -> Dict[str, Any]:
        """Default action on timeout (fold)"""
        return {"action": "fold"}
```

## Agent System

### Agent Execution Architecture

```python
# agent_executor.py
import asyncio
import json
from typing import Dict, Any, List
from llm_clients import get_llm_client
from tool_executor import ToolExecutor

class AgentExecutor:
    def __init__(self, agent_config: Dict, api_keys: Dict, tools: List[Dict], timeout: int):
        self.agent_config = agent_config
        self.api_keys = api_keys
        self.tools = tools
        self.timeout = timeout
        self.tool_executor = ToolExecutor(tools)

    async def execute_turn(self, game_state: Dict[str, Any], player_id: str) -> Dict[str, Any]:
        """Execute agent's turn with timeout"""
        start_time = time.time()

        try:
            # Execute with timeout
            result = await asyncio.wait_for(
                self._execute_agent_logic(game_state, player_id),
                timeout=self.timeout
            )

            execution_time = int((time.time() - start_time) * 1000)
            result['execution_time'] = execution_time

            return result

        except asyncio.TimeoutError:
            raise TimeoutError(f"Agent execution timed out after {self.timeout}s")

    async def _execute_agent_logic(self, game_state: Dict[str, Any], player_id: str) -> Dict[str, Any]:
        """Core agent execution logic"""
        thinking_log = []

        # Prepare context for LLM
        context = self._prepare_context(game_state, player_id)
        thinking_log.append(f"Game context prepared: {len(context)} characters")

        # Get LLM client
        llm_client = get_llm_client(
            provider=self.agent_config['llm_provider'],
            model=self.agent_config['llm_model'],
            api_key=self.api_keys.get(self.agent_config['llm_provider'])
        )

        # Execute agent reasoning
        messages = [
            {"role": "system", "content": self.agent_config['main_prompt']},
            {"role": "user", "content": context}
        ]

        # Add tool definitions if available
        if self.tools:
            messages[0]["content"] += f"\n\nAvailable tools: {self._format_tools()}"

        thinking_log.append("Sending request to LLM...")

        # Get LLM response
        response = await llm_client.chat_completion(
            messages=messages,
            tools=self._format_tools_for_llm() if self.tools else None
        )

        thinking_log.append(f"LLM response received: {response['content'][:200]}...")

        # Execute any tool calls
        if response.get('tool_calls'):
            for tool_call in response['tool_calls']:
                tool_result = await self.tool_executor.execute_tool(
                    tool_call['name'],
                    tool_call['arguments']
                )
                thinking_log.append(f"Tool {tool_call['name']} executed: {tool_result}")

        # Parse final move from response
        move = self._parse_move_from_response(response['content'])
        thinking_log.append(f"Final move parsed: {move}")

        return {
            'move': move,
            'thinking_log': '\n'.join(thinking_log)
        }

    def _prepare_context(self, game_state: Dict[str, Any], player_id: str) -> str:
        """Prepare game context for LLM"""
        context_parts = [
            f"You are playing as player {player_id}",
            f"Current game state: {json.dumps(game_state, indent=2)}",
            f"Your valid actions: {game_state.get('valid_actions', [])}",
        ]

        if game_state.get('error'):
            context_parts.append(f"Previous move error: {game_state['error']}")

        return '\n\n'.join(context_parts)
```

### Tool System

```python
# tool_executor.py
import ast
import sys
from typing import Dict, Any, List
from io import StringIO
import contextlib

class ToolExecutor:
    def __init__(self, tools: List[Dict]):
        self.tools = {tool['name']: tool for tool in tools}

    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Execute a user-defined tool safely"""
        if tool_name not in self.tools:
            raise ValueError(f"Tool {tool_name} not found")

        tool = self.tools[tool_name]
        code = tool['code']

        # Create safe execution environment
        safe_globals = {
            '__builtins__': {
                'len': len, 'str': str, 'int': int, 'float': float,
                'list': list, 'dict': dict, 'tuple': tuple,
                'min': min, 'max': max, 'sum': sum, 'abs': abs,
                'round': round, 'sorted': sorted, 'reversed': reversed,
                'enumerate': enumerate, 'zip': zip, 'range': range,
                'print': print, 'json': __import__('json'),
                'math': __import__('math'), 'random': __import__('random')
            }
        }

        # Capture stdout
        stdout_capture = StringIO()

        try:
            with contextlib.redirect_stdout(stdout_capture):
                # Execute the tool code
                exec(code, safe_globals, arguments)

                # Get the result (assume tool sets 'result' variable)
                result = arguments.get('result', stdout_capture.getvalue())

            return result

        except Exception as e:
            return f"Tool execution error: {str(e)}"
```

## Streaming & Real-time Updates

### WebSocket Architecture

```python
# websocket/connection_manager.py
from typing import Dict, Set
import json
import asyncio
from fastapi import WebSocket

class ConnectionManager:
    def __init__(self):
        # game_id -> set of websocket connections
        self.game_connections: Dict[str, Set[WebSocket]] = {}
        # user_id -> websocket connection (for private agent thinking)
        self.user_connections: Dict[str, WebSocket] = {}

    async def connect_to_game(self, websocket: WebSocket, game_id: str):
        """Connect user to game stream"""
        await websocket.accept()

        if game_id not in self.game_connections:
            self.game_connections[game_id] = set()

        self.game_connections[game_id].add(websocket)

    async def connect_user(self, websocket: WebSocket, user_id: str):
        """Connect user for private notifications"""
        await websocket.accept()
        self.user_connections[user_id] = websocket

    def disconnect_from_game(self, websocket: WebSocket, game_id: str):
        """Disconnect from game stream"""
        if game_id in self.game_connections:
            self.game_connections[game_id].discard(websocket)
            if not self.game_connections[game_id]:
                del self.game_connections[game_id]

    def disconnect_user(self, user_id: str):
        """Disconnect user"""
        self.user_connections.pop(user_id, None)

    async def broadcast_to_game(self, game_id: str, message: Dict):
        """Broadcast message to all game spectators"""
        if game_id not in self.game_connections:
            return

        message_str = json.dumps(message)
        disconnected = set()

        for websocket in self.game_connections[game_id]:
            try:
                await websocket.send_text(message_str)
            except:
                disconnected.add(websocket)

        # Clean up disconnected websockets
        for ws in disconnected:
            self.game_connections[game_id].discard(ws)

    async def send_to_user(self, user_id: str, message: Dict):
        """Send private message to specific user"""
        if user_id not in self.user_connections:
            return

        try:
            await self.user_connections[user_id].send_text(json.dumps(message))
        except:
            self.disconnect_user(user_id)

# Global connection manager instance
connection_manager = ConnectionManager()
```

### Real-time Message Types

```python
# Message types for WebSocket communication

# Game state update (public)
{
    "type": "game_update",
    "game_id": "uuid",
    "game_state": {...},
    "last_action": {
        "player_id": "uuid",
        "action": "raise",
        "amount": 100
    },
    "timestamp": "2025-07-26T10:00:00Z"
}

# Agent thinking (private to owner)
{
    "type": "agent_thinking",
    "agent_id": "uuid",
    "game_id": "uuid",
    "thinking_log": "I should raise because...",
    "timestamp": "2025-07-26T10:00:00Z"
}

# Game completed
{
    "type": "game_complete",
    "game_id": "uuid",
    "results": {
        "winners": ["uuid1"],
        "payouts": {"uuid1": 500, "uuid2": -100}
    },
    "timestamp": "2025-07-26T10:00:00Z"
}
```

## Security & Performance

### Security Measures

#### API Key Encryption
```python
# utils/encryption.py
from cryptography.fernet import Fernet
import os
import base64

class APIKeyEncryption:
    def __init__(self):
        # Use AWS KMS or environment variable for key
        self.key = os.getenv('ENCRYPTION_KEY').encode()
        self.fernet = Fernet(base64.urlsafe_b64encode(self.key[:32]))

    def encrypt_api_key(self, api_key: str) -> str:
        """Encrypt user API key for storage"""
        return self.fernet.encrypt(api_key.encode()).decode()

    def decrypt_api_key(self, encrypted_key: str) -> str:
        """Decrypt API key for use"""
        return self.fernet.decrypt(encrypted_key.encode()).decode()
```

#### Tool Execution Sandbox
- Restricted Python execution environment
- No file system access
- No network access
- Limited built-in functions
- Execution timeout (5 seconds max)
- Memory limits

#### Authentication & Authorization
- JWT tokens for API authentication
- User can only access their own agents/tools
- Agent thinking logs only visible to owner
- Rate limiting on API endpoints
- Input validation and sanitization

### Performance Optimizations

#### Database Optimizations
- Connection pooling with SQLAlchemy
- Read replicas for leaderboards and game history
- Proper indexing on frequently queried columns
- JSONB indexes for game state queries
- Partitioning for large tables (game_actions)

#### Caching Strategy
```python
# Redis caching for frequently accessed data
CACHE_PATTERNS = {
    "user_profile": 300,      # 5 minutes
    "agent_config": 600,      # 10 minutes
    "leaderboard": 60,        # 1 minute
    "game_state": 10,         # 10 seconds
    "tool_code": 1800,        # 30 minutes
}
```

#### SQS Optimizations
- Batch message processing (up to 10 messages)
- Long polling to reduce API calls
- Dead letter queues for failed messages
- Message deduplication for critical operations

## Social Sharing & Referral System

### Overview
The social sharing system allows users to share games with friends and earn bonuses through referrals. Users can post about games on social media platforms or send direct invitations to friends, with both the inviter and invitee receiving virtual currency bonuses when the invitation is accepted.

### Database Schema Extensions

```sql
-- Social sharing invitations
CREATE TABLE social_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    inviter_id UUID REFERENCES users(id) ON DELETE CASCADE,
    invitation_code VARCHAR(50) UNIQUE NOT NULL,
    invitation_type VARCHAR(20) NOT NULL, -- 'social_post', 'direct_invite'
    platform VARCHAR(20), -- 'facebook', 'instagram', 'whatsapp', 'twitter', 'linkedin'
    game_id UUID REFERENCES games(id) ON DELETE SET NULL, -- Optional: specific game being shared
    invitee_identifier VARCHAR(255), -- Email, phone, or social handle
    bonus_amount INTEGER DEFAULT 100, -- Bonus for both inviter and invitee
    inviter_bonus_claimed BOOLEAN DEFAULT false,
    invitee_bonus_claimed BOOLEAN DEFAULT false,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'expired'
    expires_at TIMESTAMP NOT NULL,
    accepted_at TIMESTAMP,
    accepted_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Social sharing analytics
CREATE TABLE social_sharing_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invitation_id UUID REFERENCES social_invitations(id) ON DELETE CASCADE,
    event_type VARCHAR(30) NOT NULL, -- 'link_clicked', 'app_opened', 'signup_started', 'signup_completed'
    user_agent TEXT,
    ip_address INET,
    referrer_url TEXT,
    platform_data JSONB, -- Platform-specific tracking data
    created_at TIMESTAMP DEFAULT NOW()
);

-- User social connections (for friend suggestions)
CREATE TABLE user_social_connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL, -- 'facebook', 'instagram', 'contacts'
    external_user_id VARCHAR(255), -- Platform-specific user ID
    friend_name VARCHAR(100),
    friend_identifier VARCHAR(255), -- Email, phone, username
    connection_strength INTEGER DEFAULT 1, -- How close the connection is
    last_interaction TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, platform, external_user_id)
);

-- Bonus tracking for referrals
CREATE TABLE referral_bonuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invitation_id UUID REFERENCES social_invitations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    bonus_type VARCHAR(20) NOT NULL, -- 'inviter_bonus', 'invitee_bonus'
    amount INTEGER NOT NULL,
    claimed_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### API Endpoints

```python
# api/routes/social.py
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from schemas.social import (
    SocialInvitationCreate, SocialInvitationResponse,
    SocialShareRequest, SocialShareResponse,
    FriendSuggestion, ReferralBonusResponse
)

router = APIRouter(prefix="/api/v1/social", tags=["social"])

@router.post("/share/game", response_model=SocialShareResponse)
async def share_game(
    request: SocialShareRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create shareable link for a game with bonus incentive"""
    service = SocialSharingService(db)
    return await service.create_game_share_link(
        user_id=current_user.id,
        game_id=request.game_id,
        platform=request.platform,
        message=request.custom_message
    )

@router.post("/invite/direct", response_model=SocialInvitationResponse)
async def send_direct_invitation(
    request: SocialInvitationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send direct invitation to specific friends"""
    service = SocialSharingService(db)
    return await service.send_direct_invitations(
        inviter_id=current_user.id,
        invitees=request.invitees,
        platform=request.platform,
        custom_message=request.custom_message
    )

@router.get("/friends/suggestions", response_model=List[FriendSuggestion])
async def get_friend_suggestions(
    platform: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get friend suggestions from connected social platforms"""
    service = SocialSharingService(db)
    return await service.get_friend_suggestions(
        user_id=current_user.id,
        platform=platform
    )

@router.post("/accept-invitation/{invitation_code}")
async def accept_invitation(
    invitation_code: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Accept a social invitation and claim bonuses"""
    service = SocialSharingService(db)
    return await service.accept_invitation(
        invitation_code=invitation_code,
        accepting_user_id=current_user.id
    )

@router.get("/bonuses/pending", response_model=List[ReferralBonusResponse])
async def get_pending_bonuses(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get pending referral bonuses for user"""
    service = SocialSharingService(db)
    return await service.get_pending_bonuses(current_user.id)

@router.post("/bonuses/claim/{invitation_id}")
async def claim_referral_bonus(
    invitation_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Claim referral bonus from successful invitation"""
    service = SocialSharingService(db)
    return await service.claim_referral_bonus(
        invitation_id=invitation_id,
        user_id=current_user.id
    )
```

### Social Sharing Service

```python
# services/social_sharing_service.py
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from models.social import SocialInvitation, SocialSharingAnalytics, ReferralBonus
from models.user import User
from models.game import Game
from core.enums import SocialPlatform, InvitationType
import secrets
import string

class SocialSharingService:
    def __init__(self, db: Session):
        self.db = db
        self.base_url = os.getenv('FRONTEND_URL', 'https://agenttarena.com')

    async def create_game_share_link(self, user_id: str, game_id: Optional[str] = None,
                                   platform: str = None, message: str = None) -> Dict[str, Any]:
        """Create shareable link for social media posting"""

        # Generate unique invitation code
        invitation_code = self._generate_invitation_code()

        # Create invitation record
        invitation = SocialInvitation(
            inviter_id=user_id,
            invitation_code=invitation_code,
            invitation_type=InvitationType.SOCIAL_POST,
            platform=platform,
            game_id=game_id,
            bonus_amount=self._get_bonus_amount_for_platform(platform),
            expires_at=datetime.utcnow() + timedelta(days=30)
        )

        self.db.add(invitation)
        self.db.commit()

        # Generate share URL
        share_url = f"{self.base_url}/join/{invitation_code}"

        # Create platform-specific share content
        share_content = self._create_share_content(
            platform=platform,
            share_url=share_url,
            game_id=game_id,
            custom_message=message,
            bonus_amount=invitation.bonus_amount
        )

        return {
            "invitation_id": invitation.id,
            "share_url": share_url,
            "share_content": share_content,
            "bonus_amount": invitation.bonus_amount,
            "expires_at": invitation.expires_at
        }

    async def send_direct_invitations(self, inviter_id: str, invitees: List[Dict[str, str]],
                                    platform: str, custom_message: str = None) -> Dict[str, Any]:
        """Send direct invitations to specific friends"""

        invitations_created = []

        for invitee in invitees:
            # Generate unique invitation code for each invitee
            invitation_code = self._generate_invitation_code()

            invitation = SocialInvitation(
                inviter_id=inviter_id,
                invitation_code=invitation_code,
                invitation_type=InvitationType.DIRECT_INVITE,
                platform=platform,
                invitee_identifier=invitee.get('identifier'),  # email, phone, or username
                bonus_amount=self._get_bonus_amount_for_platform(platform),
                expires_at=datetime.utcnow() + timedelta(days=7)  # Shorter expiry for direct invites
            )

            self.db.add(invitation)
            self.db.flush()  # Get ID

            # Send invitation via appropriate channel
            await self._send_invitation_message(
                invitation=invitation,
                invitee_info=invitee,
                platform=platform,
                custom_message=custom_message
            )

            invitations_created.append({
                "invitation_id": invitation.id,
                "invitee": invitee.get('name', invitee.get('identifier')),
                "invitation_url": f"{self.base_url}/join/{invitation_code}",
                "expires_at": invitation.expires_at
            })

        self.db.commit()

        return {
            "invitations_sent": len(invitations_created),
            "invitations": invitations_created,
            "bonus_per_acceptance": invitations_created[0]["bonus_amount"] if invitations_created else 0
        }

    async def accept_invitation(self, invitation_code: str, accepting_user_id: str) -> Dict[str, Any]:
        """Accept invitation and process bonuses"""

        invitation = self.db.query(SocialInvitation).filter(
            SocialInvitation.invitation_code == invitation_code,
            SocialInvitation.status == 'pending'
        ).first()

        if not invitation:
            raise HTTPException(status_code=404, detail="Invitation not found or already used")

        if invitation.expires_at < datetime.utcnow():
            raise HTTPException(status_code=400, detail="Invitation has expired")

        # Check if user is trying to accept their own invitation
        if invitation.inviter_id == accepting_user_id:
            raise HTTPException(status_code=400, detail="Cannot accept your own invitation")

        # Update invitation status
        invitation.status = 'accepted'
        invitation.accepted_at = datetime.utcnow()
        invitation.accepted_by_user_id = accepting_user_id

        # Award bonuses
        inviter_bonus = await self._award_bonus(
            user_id=invitation.inviter_id,
            invitation_id=invitation.id,
            bonus_type='inviter_bonus',
            amount=invitation.bonus_amount
        )

        invitee_bonus = await self._award_bonus(
            user_id=accepting_user_id,
            invitation_id=invitation.id,
            bonus_type='invitee_bonus',
            amount=invitation.bonus_amount
        )

        self.db.commit()

        return {
            "message": "Invitation accepted successfully!",
            "inviter_bonus": inviter_bonus,
            "invitee_bonus": invitee_bonus,
            "total_bonus_awarded": invitation.bonus_amount * 2
        }

    def _generate_invitation_code(self) -> str:
        """Generate unique invitation code"""
        while True:
            code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
            existing = self.db.query(SocialInvitation).filter(
                SocialInvitation.invitation_code == code
            ).first()
            if not existing:
                return code

    def _get_bonus_amount_for_platform(self, platform: str) -> int:
        """Get bonus amount based on platform (some platforms might have higher bonuses)"""
        platform_bonuses = {
            'facebook': 150,
            'instagram': 150,
            'twitter': 100,
            'linkedin': 100,
            'whatsapp': 200,  # Higher bonus for direct messaging
            'email': 200,
            'sms': 200
        }
        return platform_bonuses.get(platform, 100)

    def _create_share_content(self, platform: str, share_url: str, game_id: str = None,
                            custom_message: str = None, bonus_amount: int = 100) -> Dict[str, str]:
        """Create platform-specific share content"""

        base_message = custom_message or "Join me in Agent League - the ultimate AI agent battle platform!"
        bonus_text = f"Use my invite link and we both get ${bonus_amount} bonus!"

        platform_content = {
            'facebook': {
                'text': f"{base_message} 🤖⚔️\n\n{bonus_text}\n\n{share_url}",
                'hashtags': ['#AgentArena', '#AIGaming', '#PokerAI']
            },
            'instagram': {
                'text': f"{base_message} 🤖⚔️\n\n{bonus_text}\n\nLink in bio: {share_url}",
                'hashtags': ['#AgentArena', '#AIGaming', '#PokerAI', '#TechGaming']
            },
            'twitter': {
                'text': f"{base_message} 🤖⚔️\n\n{bonus_text}\n\n{share_url}\n\n#AgentArena #AIGaming",
                'hashtags': ['#AgentArena', '#AIGaming', '#PokerAI']
            },
            'whatsapp': {
                'text': f"Hey! {base_message}\n\n{bonus_text}\n\nJoin here: {share_url}"
            },
            'linkedin': {
                'text': f"{base_message}\n\n{bonus_text}\n\nCheck it out: {share_url}",
                'hashtags': ['#AgentArena', '#ArtificialIntelligence', '#Gaming']
            }
        }

        return platform_content.get(platform, {
            'text': f"{base_message}\n\n{bonus_text}\n\n{share_url}"
        })

    async def _send_invitation_message(self, invitation: SocialInvitation, invitee_info: Dict[str, str],
                                     platform: str, custom_message: str = None):
        """Send invitation message via appropriate platform"""

        invitation_url = f"{self.base_url}/join/{invitation.invitation_code}"
        message = custom_message or f"Join me on Agent League! Use this link and we both get ${invitation.bonus_amount} bonus: {invitation_url}"

        if platform == 'email':
            await self._send_email_invitation(invitee_info['identifier'], message, invitation_url)
        elif platform == 'sms':
            await self._send_sms_invitation(invitee_info['identifier'], message)
        elif platform == 'whatsapp':
            # For WhatsApp, we create a pre-filled message URL
            whatsapp_url = f"https://wa.me/{invitee_info['identifier']}?text={message}"
            # Store the URL for the frontend to open
            pass
        # For social platforms, the frontend handles the sharing

    async def _award_bonus(self, user_id: str, invitation_id: str, bonus_type: str, amount: int) -> Dict[str, Any]:
        """Award bonus to user and create bonus record"""

        # Update user's virtual currency
        user = self.db.query(User).filter(User.id == user_id).first()
        user.virtual_currency += amount

        # Create bonus record
        bonus = ReferralBonus(
            invitation_id=invitation_id,
            user_id=user_id,
            bonus_type=bonus_type,
            amount=amount
        )
        self.db.add(bonus)

        return {
            "user_id": user_id,
            "bonus_type": bonus_type,
            "amount": amount,
            "new_balance": user.virtual_currency
        }
```

### Frontend Components

```typescript
// components/social/SocialShareModal.tsx
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Share2, Users, MessageCircle, Mail, Phone } from 'lucide-react';

interface SocialShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  gameId?: string;
}

export const SocialShareModal: React.FC<SocialShareModalProps> = ({
  isOpen,
  onClose,
  gameId
}) => {
  const [shareType, setShareType] = useState<'social' | 'direct'>('social');
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');
  const [customMessage, setCustomMessage] = useState('');
  const [invitees, setInvitees] = useState<Array<{name: string, identifier: string}>>([]);
  const [loading, setLoading] = useState(false);

  const socialPlatforms = [
    { id: 'facebook', name: 'Facebook', icon: '📘', bonus: 150 },
    { id: 'instagram', name: 'Instagram', icon: '📷', bonus: 150 },
    { id: 'twitter', name: 'Twitter', icon: '🐦', bonus: 100 },
    { id: 'linkedin', name: 'LinkedIn', icon: '💼', bonus: 100 },
    { id: 'whatsapp', name: 'WhatsApp', icon: '💬', bonus: 200 }
  ];

  const directPlatforms = [
    { id: 'email', name: 'Email', icon: '📧', bonus: 200 },
    { id: 'sms', name: 'SMS', icon: '📱', bonus: 200 },
    { id: 'whatsapp', name: 'WhatsApp', icon: '💬', bonus: 200 }
  ];

  const handleSocialShare = async (platform: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/social/share/game', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          game_id: gameId,
          platform,
          custom_message: customMessage
        })
      });

      const data = await response.json();

      // Open platform-specific sharing
      if (platform === 'facebook') {
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(data.share_url)}&quote=${encodeURIComponent(data.share_content.text)}`);
      } else if (platform === 'twitter') {
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(data.share_content.text)}`);
      } else if (platform === 'whatsapp') {
        window.open(`https://wa.me/?text=${encodeURIComponent(data.share_content.text)}`);
      }
      // Add other platforms...

    } catch (error) {
      console.error('Share failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDirectInvite = async () => {
    if (invitees.length === 0) return;

    setLoading(true);
    try {
      const response = await fetch('/api/v1/social/invite/direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          invitees,
          platform: selectedPlatform,
          custom_message: customMessage
        })
      });

      const data = await response.json();
      alert(`Sent ${data.invitations_sent} invitations! You'll earn $${data.bonus_per_acceptance} for each friend who joins.`);
      onClose();

    } catch (error) {
      console.error('Invite failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            Share & Earn Bonuses
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Share Type Selection */}
          <div className="flex gap-4">
            <Button
              variant={shareType === 'social' ? 'default' : 'outline'}
              onClick={() => setShareType('social')}
              className="flex-1"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Social Media
            </Button>
            <Button
              variant={shareType === 'direct' ? 'default' : 'outline'}
              onClick={() => setShareType('direct')}
              className="flex-1"
            >
              <Users className="w-4 h-4 mr-2" />
              Direct Invite
            </Button>
          </div>

          {/* Custom Message */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Custom Message (Optional)
            </label>
            <Textarea
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              placeholder="Add your personal message..."
              className="w-full"
            />
          </div>

          {shareType === 'social' ? (
            /* Social Media Sharing */
            <div>
              <h3 className="font-medium mb-3">Choose Platform</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {socialPlatforms.map((platform) => (
                  <Button
                    key={platform.id}
                    variant="outline"
                    onClick={() => handleSocialShare(platform.id)}
                    disabled={loading}
                    className="flex flex-col items-center p-4 h-auto"
                  >
                    <span className="text-2xl mb-1">{platform.icon}</span>
                    <span className="text-sm">{platform.name}</span>
                    <span className="text-xs text-green-600">${platform.bonus} bonus</span>
                  </Button>
                ))}
              </div>
            </div>
          ) : (
            /* Direct Invitations */
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-3">Invite Friends Directly</h3>
                <div className="flex gap-2 mb-3">
                  {directPlatforms.map((platform) => (
                    <Button
                      key={platform.id}
                      variant={selectedPlatform === platform.id ? 'default' : 'outline'}
                      onClick={() => setSelectedPlatform(platform.id)}
                      size="sm"
                    >
                      {platform.icon} {platform.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Friend Input */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Add Friends ({selectedPlatform === 'email' ? 'Email' : selectedPlatform === 'sms' ? 'Phone' : 'Contact'})
                </label>
                <div className="flex gap-2">
                  <Input
                    placeholder={selectedPlatform === 'email' ? '<EMAIL>' : '+1234567890'}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        const value = e.currentTarget.value.trim();
                        if (value) {
                          setInvitees([...invitees, { name: '', identifier: value }]);
                          e.currentTarget.value = '';
                        }
                      }
                    }}
                  />
                  <Button size="sm">Add</Button>
                </div>

                {/* Invitee List */}
                {invitees.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {invitees.map((invitee, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                        <span>{invitee.identifier}</span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => setInvitees(invitees.filter((_, i) => i !== index))}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <Button
                onClick={handleDirectInvite}
                disabled={loading || invitees.length === 0 || !selectedPlatform}
                className="w-full"
              >
                Send {invitees.length} Invitation{invitees.length !== 1 ? 's' : ''}
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
```

### Integration Points

#### Game Interface Integration
- Add share button to game tables and completed games
- Show bonus notifications when friends join through invites
- Display referral earnings in user dashboard

#### User Dashboard Integration
- Referral statistics and earnings
- Pending invitations status
- Friend suggestions from connected social accounts

#### Notification System Integration
- Real-time notifications when invitations are accepted
- Bonus award notifications
- Reminder notifications for pending invitations

### Security Considerations

1. **Rate Limiting**: Limit number of invitations per user per day
2. **Fraud Prevention**: Track IP addresses and user agents to prevent abuse
3. **Invitation Validation**: Verify invitation codes and expiry dates
4. **Bonus Limits**: Set maximum bonus amounts per user per period
5. **Platform Verification**: Validate social platform integrations

### Analytics & Tracking

- Track invitation conversion rates by platform
- Monitor bonus distribution and ROI
- Analyze viral coefficient and user acquisition costs
- A/B test different bonus amounts and messaging

## Deployment Architecture

### AWS Infrastructure

```yaml
# infrastructure/docker-compose.yml (for local development)
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: agent_arena
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7
    ports:
      - "6379:6379"

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: ********************************************/agent_arena
      REDIS_URL: redis://redis:6379
    depends_on:
      - postgres
      - redis

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend

  game_manager:
    build: ./game_manager
    environment:
      DATABASE_URL: ********************************************/agent_arena
      INSTANCE_ID: game_manager_1
    depends_on:
      - postgres

volumes:
  postgres_data:
```

### Production Deployment (AWS)

#### Infrastructure Components
- **ECS Fargate**: Game Managers (auto-scaling)
- **Lambda**: Agent execution (event-driven scaling)
- **Aurora PostgreSQL**: Database with read replicas
- **ElastiCache Redis**: Caching layer
- **SQS**: Message queues with DLQ
- **API Gateway**: WebSocket and HTTP APIs
- **CloudFront**: CDN for frontend
- **S3**: Static assets and game replays
- **CloudWatch**: Monitoring and logging
- **AWS KMS**: Encryption key management

#### Scaling Strategy
```python
# Auto-scaling configuration
SCALING_POLICIES = {
    "game_managers": {
        "min_instances": 2,
        "max_instances": 20,
        "target_cpu": 70,
        "scale_up_cooldown": 300,
        "scale_down_cooldown": 600
    },
    "agent_lambdas": {
        "concurrent_executions": 1000,
        "timeout": 30,
        "memory": 512
    }
}
```

### Monitoring & Observability

#### Key Metrics
- Game completion rate
- Agent execution time
- Queue depth and processing time
- Database connection pool usage
- WebSocket connection count
- Error rates by component

#### Alerting
- Game manager heartbeat failures
- High queue depth (>1000 messages)
- Database connection exhaustion
- Lambda timeout rate >5%
- WebSocket connection drops

### Development Workflow

#### Local Development Setup
```bash
# Setup local environment
git clone <repo>
cd agent_arena

# Start infrastructure
docker-compose up -d postgres redis

# Setup backend using uv workspace
uv sync
uv run --package shared_db alembic upgrade head
uv run --package backend uvicorn app.main:app --reload --port 9000

# Setup frontend
cd ../frontend
npm install
npm run dev

# Start game manager
cd ../game_manager
python main.py
```

#### Testing Strategy
- Unit tests for business logic
- Integration tests for API endpoints
- End-to-end tests for game flows
- Load testing for concurrent games
- Chaos engineering for fault tolerance
****
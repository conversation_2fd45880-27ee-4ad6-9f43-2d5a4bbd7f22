# TODO:

- [x] 20: Fix massive chip inflation in side pot tests (e.g., 2565 instead of 450) (priority: High)
- [x] 21: Fix remaining state diff mismatches in side pot and payout tests (priority: High)
- [x] 24: Fix 'No last state diff recorded' errors in test_proper_patterns.py (priority: High)
- [x] 26: Fix remaining test failures (now 5 failed in position management, ~120 total) - focus on state diff mismatches (priority: High)
- [x] 30: Fix chip conservation issues in test_side_pots.py - incorrect expected totals (priority: High)
- [x] 31: Calculate correct expected total chips for test_complex_side_pot_distribution based on actual chip distribution (priority: High)
- [x] 32: Fix state diff mismatches in side pot tests - exclude_fields not working properly (priority: High)
- [x] 33: Fix state diff mismatches in basic move tests - add missing expected state fields (priority: High)
- [x] 34: Fix chip conservation errors in simple payout tests (priority: High)
- [x] 35: Fix test_side_pots_fixed.py - all 7 tests now passing with correct pot values and state diffs (priority: High)
- [x] 36: Fix test_betting_round_progression - corrected blind positions and player sequence for 3-player game (priority: High)
- [x] 37: Fix remaining 4 failures in test_proper_patterns.py - state diff mismatches and error handling (priority: High)
- [x] 38: Fix chip conservation issues in test_simple_payouts.py - calculate initial total including blinds (priority: High)
- [x] 39: Fix PokerTest.create to always default to 5 players instead of inferring from chips dictionary (priority: High)
- [x] 40: Fix test_position_management.py - 9 tests now passing, fixed 'No last state diff recorded' errors (priority: High)
- [x] 41: Fix side pot test failures - state diff mismatches and side pot creation issues (priority: High)
- [x] 42: Remove debug print statements from test_helpers.py (priority: High)
- [x] 43: Fix ValidationError in test_basic_moves.py and minimum raise validation test (priority: High)
- [ ] 44: Fix any remaining test failures while preserving blind positioning logic (**IN PROGRESS**) (priority: High)
- [ ] 45: CRITICAL RULE: NEVER infer player count from chips dictionary in PokerTest.create - it must ALWAYS be explicitly set as num_players parameter (priority: High)
- [ ] 3: Replace assert False with proper AssertionError raises (priority: Medium)
- [ ] 4: Fix pytest.raises usage to be more specific than Exception (priority: Medium)
- [ ] 6: Fix try-except-pass patterns with proper error handling (priority: Medium)
- [ ] 7: Ensure all tests use explicit player IDs instead of getters (priority: Medium)
- [ ] 8: Verify all tests use PokerTest.create for state setup (priority: Medium)
- [ ] 27: Investigate fundamental chip calculation issues in side pot scenarios (priority: Medium)
- [ ] 5: Replace if-else blocks with ternary operators where suggested (priority: Low)

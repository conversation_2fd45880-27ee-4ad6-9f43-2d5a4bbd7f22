[alembic]
script_location = alembic
# The sqlalchemy.url is set in env.py from the config service
# sqlalchemy.url = postgresql://<username>:<password>@<host>:<port>/<database>
# This is the path to the migration scripts
# The default is "versions", but you can change it if needed
version_locations = %(script_location)s/versions

[loggers]
keys = root, sqlalchemy, alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = INFO
handlers = console

[logger_sqlalchemy]
level = WARN
handlers = console
qualname = sqlalchemy.engine
propagate = 0

[logger_alembic]
level = INFO
handlers = console
qualname = alembic
propagate = 0

[handler_console]
class = StreamHandler
args = []
level = DEBUG
formatter = generic

[formatter_generic]
format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
datefmt = %Y-%m-%d %H:%M:%S
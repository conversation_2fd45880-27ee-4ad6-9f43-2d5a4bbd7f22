"""Texas Hold'em implementation for the game engine framework."""

from __future__ import annotations

import random
from enum import IntEnum, StrEnum
from itertools import combinations
from typing import override

from pydantic import BaseModel, Field, field_validator, model_validator

from common.utils.tsid import TSID
from libs.game.game_api.core import GameConfig, GameEnv, GameState, GameType, PlayerId, PlayerMove
from libs.game.texas_holdem.texas_holdem_errors import TexasHoldemErrors as THErrors

# Player ID constants
PLAYER_1 = PlayerId("player_1")
PLAYER_2 = PlayerId("player_2")
PLAYER_3 = PlayerId("player_3")
PLAYER_4 = PlayerId("player_4")
PLAYER_5 = PlayerId("player_5")

class SidePot(BaseModel):
    """Represents a side pot in poker."""

    amount: int = Field(..., description="Amount in the side pot", ge=0)
    eligible_players: list[PlayerId] = Field(..., description="Player IDs eligible for this side pot")

    @field_validator("eligible_players")
    @classmethod
    def validate_eligible_players(cls, v: list[PlayerId]) -> list[PlayerId]:
        """Validate eligible players list."""
        if len(v) < 1:
            raise ValueError("Side pot must have at least one eligible player")
        return v


class TexasHoldemAction(StrEnum):
    """Possible Texas Hold'em actions."""

    FOLD = "fold"
    CHECK = "check"
    CALL = "call"
    RAISE = "raise"
    ALL_IN = "all_in"


class PlayerStatus(StrEnum):
    """Player status in a poker game."""

    ACTIVE = "active"
    FOLDED = "folded"
    ALL_IN = "all_in"
    OUT = "out"


class BettingRound(IntEnum):
    """Betting rounds in Texas Hold'em."""

    PREFLOP = 1
    FLOP = 2
    TURN = 3
    RIVER = 4
    SHOWDOWN = 5


class CardRank(StrEnum):
    """Card ranks as string enums for readability while supporting numeric mapping."""

    TWO = "2"
    THREE = "3"
    FOUR = "4"
    FIVE = "5"
    SIX = "6"
    SEVEN = "7"
    EIGHT = "8"
    NINE = "9"
    TEN = "10"
    JACK = "J"
    QUEEN = "Q"
    KING = "K"
    ACE = "A"

    def as_int(self) -> int:
        return _RANK_TO_INT[self]

    @staticmethod
    def of(rank: int | str) -> CardRank:
        return CardRank(str(rank))


_RANK_TO_INT: dict[CardRank, int] = {
    CardRank.TWO: 2,
    CardRank.THREE: 3,
    CardRank.FOUR: 4,
    CardRank.FIVE: 5,
    CardRank.SIX: 6,
    CardRank.SEVEN: 7,
    CardRank.EIGHT: 8,
    CardRank.NINE: 9,
    CardRank.TEN: 10,
    CardRank.JACK: 11,
    CardRank.QUEEN: 12,
    CardRank.KING: 13,
    CardRank.ACE: 14,
}


class CardSuit(StrEnum):
    """Card suits as string enums."""

    HEARTS = "hearts"
    DIAMONDS = "diamonds"
    CLUBS = "clubs"
    SPADES = "spades"

    def as_symbol(self) -> str:
        return _SUITE_TO_SYMBOL[self]


_SUITE_TO_SYMBOL: dict[CardSuit, str] = {
    CardSuit.HEARTS: "♥",
    CardSuit.DIAMONDS: "♦",
    CardSuit.CLUBS: "♣",
    CardSuit.SPADES: "♠",
}

class HandRank(IntEnum):
    """Poker hand rankings from lowest to highest."""

    HIGH_CARD = 1
    PAIR = 2
    TWO_PAIR = 3
    THREE_OF_A_KIND = 4
    STRAIGHT = 5
    FLUSH = 6
    FULL_HOUSE = 7
    FOUR_OF_A_KIND = 8
    STRAIGHT_FLUSH = 9
    ROYAL_FLUSH = 10


class Card(BaseModel):
    """Represents a playing card."""

    rank: CardRank = Field(..., description="Card rank (2-10, J, Q, K, A)")
    suit: CardSuit = Field(..., description="Card suit (hearts, diamonds, clubs, spades)")

    @staticmethod
    def of(card_str_or_rank: str | CardRank | int, suit: CardSuit | str | None = None) -> Card:
        """Create a Card from either a single string (e.g., 'Ah', 'Kd', '10c') or rank and suit.

        Args:
            card_str_or_rank: Either a card string like 'Ah' or a rank value
            suit: Suit value (required if first arg is rank, ignored if first arg is card string)

        Returns:
            Card instance
        """
        if isinstance(card_str_or_rank, str) and suit is None:
            # Single string format like 'Ah', 'Kd', '10c'
            card_str = card_str_or_rank.strip()
            if len(card_str) < 2:
                raise ValueError(f"Invalid card string: {card_str}")

            # Last character is suit
            suit_char = card_str[-1].lower()
            rank_str = card_str[:-1]

            # Map suit characters to CardSuit
            suit_mapping = {
                "h": CardSuit.HEARTS,
                "d": CardSuit.DIAMONDS,
                "c": CardSuit.CLUBS,
                "s": CardSuit.SPADES,
            }

            if suit_char not in suit_mapping:
                raise ValueError(f"Invalid suit character: {suit_char}")

            return Card(rank=CardRank.of(rank_str), suit=suit_mapping[suit_char])
        else:
            # Two-parameter format (backward compatibility)
            if suit is None:
                raise ValueError("Suit is required when first argument is not a card string")
            return Card(rank=CardRank.of(card_str_or_rank), suit=CardSuit(suit))

    @field_validator("rank", mode="before")
    @classmethod
    def coerce_rank(cls, v: object) -> CardRank:
        """Allow constructing Card with int or str rank values by coercing to CardRank enum."""
        if isinstance(v, CardRank):
            return v
        if isinstance(v, int):
            mapping = {
                2: CardRank.TWO,
                3: CardRank.THREE,
                4: CardRank.FOUR,
                5: CardRank.FIVE,
                6: CardRank.SIX,
                7: CardRank.SEVEN,
                8: CardRank.EIGHT,
                9: CardRank.NINE,
                10: CardRank.TEN,
                11: CardRank.JACK,
                12: CardRank.QUEEN,
                13: CardRank.KING,
                14: CardRank.ACE,
            }
            if v in mapping:
                return mapping[v]
            raise ValueError(f"Invalid rank: {v}")
        if isinstance(v, str):
            s = v.upper()
            mapping = {
                "2": CardRank.TWO,
                "3": CardRank.THREE,
                "4": CardRank.FOUR,
                "5": CardRank.FIVE,
                "6": CardRank.SIX,
                "7": CardRank.SEVEN,
                "8": CardRank.EIGHT,
                "9": CardRank.NINE,
                "10": CardRank.TEN,
                "J": CardRank.JACK,
                "Q": CardRank.QUEEN,
                "K": CardRank.KING,
                "A": CardRank.ACE,
            }
            if s in mapping:
                return mapping[s]
            raise ValueError(f"Invalid rank: {v}")
        raise ValueError(f"Invalid rank type: {type(v)}")

    @field_validator("suit", mode="before")
    @classmethod
    def coerce_suit(cls, v: object) -> CardSuit:
        """Allow constructing Card with str suit values by coercing to CardSuit enum."""
        if isinstance(v, CardSuit):
            return v
        if isinstance(v, str):
            mapping = {
                "hearts": CardSuit.HEARTS,
                "diamonds": CardSuit.DIAMONDS,
                "clubs": CardSuit.CLUBS,
                "spades": CardSuit.SPADES,
            }
            s = v.lower()
            if s in mapping:
                return mapping[s]
            raise ValueError(f"Invalid suit: {v}")
        raise ValueError(f"Invalid suit type: {type(v)}")

    def __str__(self) -> str:
        """String representation of the card."""
        return f"{self.rank.value}{self.suit.as_symbol()}"

    def __hash__(self) -> int:
        """Make Card hashable for use in sets and as dict keys."""
        return hash((self.rank, self.suit))

    def __eq__(self, other: object) -> bool:
        """Check equality between cards."""
        if not isinstance(other, Card):
            return False
        return self.rank == other.rank and self.suit == other.suit


DECK = [
    Card(rank=rank, suit=suit)
    for suit in [
        CardSuit.HEARTS,
        CardSuit.DIAMONDS,
        CardSuit.CLUBS,
        CardSuit.SPADES,
    ]
    for rank in [
        CardRank.TWO,
        CardRank.THREE,
        CardRank.FOUR,
        CardRank.FIVE,
        CardRank.SIX,
        CardRank.SEVEN,
        CardRank.EIGHT,
        CardRank.NINE,
        CardRank.TEN,
        CardRank.JACK,
        CardRank.QUEEN,
        CardRank.KING,
        CardRank.ACE,
    ]
]


class HandResult(BaseModel):
    """Result of evaluating a poker hand."""

    rank: HandRank = Field(..., description="Hand rank")
    high_cards: list[int] = Field(..., description="High cards for tie-breaking")
    description: str = Field(..., description="Human-readable description")

    @property
    def high_card(self) -> int:
        """Get the highest card value."""
        return self.high_cards[0] if self.high_cards else 0

    @property
    def kicker(self) -> int:
        """Get the kicker (second highest relevant card)."""
        return self.high_cards[1] if len(self.high_cards) > 1 else 0

    def __lt__(self, other: HandResult) -> bool:
        """Compare hands for ranking."""
        if self.rank != other.rank:
            return self.rank < other.rank
        return self.high_cards < other.high_cards


class TexasHoldemPlayer(BaseModel):
    """Represents a player in a Texas Hold'em game."""

    player_id: PlayerId = Field(..., description="Unique player identifier")
    chips: int = Field(..., description="Number of chips the player has", ge=0)
    status: PlayerStatus = Field(PlayerStatus.ACTIVE, description="Current player status")
    hole_cards: list[Card] = Field(default_factory=list, description="Player's hole cards")
    current_bet: int = Field(default=0, description="Amount bet in current round", ge=0)
    total_bet: int = Field(default=0, description="Total amount bet in the hand", ge=0)

    @field_validator("hole_cards")
    @classmethod
    def validate_hole_cards(cls, v: list[Card]) -> list[Card]:
        """Validate hole cards."""
        if len(v) > 2:
            raise ValueError("Player cannot have more than 2 hole cards")
        return v


class TexasHoldemConfig(GameConfig):
    """Configuration for Texas Hold'em games."""

    game_type: GameType = Field(default=GameType.TEXAS_HOLDEM, description="Game type")
    small_blind: int = Field(..., description="Small blind amount", gt=0)
    big_blind: int = Field(..., description="Big blind amount", gt=0)
    starting_chips: int = Field(..., description="Starting chips per player", gt=0)
    starting_chips_override_for_test: dict[PlayerId, int] | None = Field(default=None, description="Starting chips per player for testing")
    default_dealer_position: int = Field(default=0, description="Default dealer position")
    min_raise: int | None = Field(default=None, description="Minimum raise amount")
    max_raise: int | None = Field(default=None, description="Maximum raise amount")

    @model_validator(mode="after")
    def validate_config(self) -> TexasHoldemConfig:
        """Validate configuration after initialization."""
        if self.big_blind <= self.small_blind:
            raise THErrors.VALIDATION_ERROR.create(
                message="Big blind must be greater than small blind",
                details={"big_blind": self.big_blind, "small_blind": self.small_blind},
            )
        if self.min_raise is not None and self.min_raise < self.big_blind:
            raise THErrors.VALIDATION_ERROR.create(
                message="Minimum raise must be at least the big blind",
                details={"min_raise": self.min_raise, "big_blind": self.big_blind},
            )
        if self.max_raise is not None and self.min_raise is not None and self.max_raise < self.min_raise:
            raise THErrors.VALIDATION_ERROR.create(
                message="Maximum raise must be at least the minimum raise",
                details={"max_raise": self.max_raise, "min_raise": self.min_raise},
            )
        return self


class TexasHoldemState(GameState):
    """Game state for Texas Hold'em."""

    game_type: GameType = Field(GameType.TEXAS_HOLDEM, description="Game type")
    players: list[TexasHoldemPlayer] = Field(..., description="Players in the game")
    community_cards: list[Card] = Field(default_factory=list, description="Community cards")
    pot: int = Field(default=0, description="Current pot amount", ge=0)
    side_pots: list[SidePot] = Field(default_factory=list, description="Side pots for all-in scenarios")
    current_bet: int = Field(default=0, description="Current bet amount to call", ge=0)
    betting_round: BettingRound = Field(default=BettingRound.PREFLOP, description="Current betting round")
    # round_number: BettingRound = Field(default=BettingRound.PREFLOP, description="Current round number", ge=1)
    dealer_position: int = Field(default=0, description="Dealer position", ge=0)
    small_blind_position: int = Field(default=0, description="Small blind position", ge=0)
    big_blind_position: int = Field(default=0, description="Big blind position", ge=0)
    action_position: int = Field(default=0, description="Position of player to act", ge=0)
    last_raise_amount: int = Field(default=0, description="Amount of last raise", ge=0)
    last_raise_position: int | None = Field(default=None, description="Position of last raiser")
    acted_positions: set[int] = Field(default_factory=set, description="Positions of players who have acted this round")
    deck: list[Card] = Field(default_factory=list, description="Remaining cards in deck")
    winners: list[PlayerId] = Field(default_factory=list, description="Winner player IDs if game is over")
    winning_hands: dict[PlayerId, HandResult] = Field(default_factory=dict, description="Winning hands by player ID")

    @model_validator(mode="after")
    def validate_state(self) -> TexasHoldemState:
        if len(self.players) < 2:
            raise THErrors.VALIDATION_ERROR.create(message="Must have at least 2 players", details={"min_players": 2, "actual_players": len(self.players)})
        if len(self.players) > 5:
            raise THErrors.VALIDATION_ERROR.create(message="Cannot have more than 5 players", details={"max_players": 5, "actual_players": len(self.players)})

        # expected_community_card_count = 0
        # match self.betting_round:
        #     case BettingRound.PREFLOP:
        #         expected_community_card_count = 0
        #     case BettingRound.FLOP:
        #         expected_community_card_count = 3
        #     case BettingRound.TURN:
        #         expected_community_card_count = 4
        #     case BettingRound.RIVER:
        #         expected_community_card_count = 5
        #     case BettingRound.SHOWDOWN:
        #         expected_community_card_count = 5
        #     case _:  # type: ignore
        #         raise THErrors.VALIDATION_ERROR.create(
        #             message=f"Invalid betting round: {self.betting_round.value}",
        #             details={"betting_round": self.betting_round.value},
        #         )
        # if len(self.community_cards) != expected_community_card_count:
        #     raise THErrors.VALIDATION_ERROR.create(
        #         message=f"Invalid game state: betting round {self.betting_round.value} should have {expected_community_card_count} community cards, but has {len(self.community_cards)}",
        #         details={
        #             "betting_round": self.betting_round.value,
        #             "expected_cards": expected_community_card_count,
        #             "actual_cards": len(self.community_cards),
        #         },
        #     )
        return self

    def get_player_by_id(self, player_id: PlayerId) -> TexasHoldemPlayer:
        """Get player by ID."""
        for player in self.players:
            if player.player_id == player_id:
                return player
        raise THErrors.INVALID_GAME_STATE.create(message=f"Player ID {player_id} not found", details={"player_id": player_id})

    def get_active_players(self) -> list[TexasHoldemPlayer]:
        """Get all active players."""
        return [p for p in self.players if p.status == PlayerStatus.ACTIVE]


class TexasHoldemMove(PlayerMove):
    """Player move in Texas Hold'em."""

    action: TexasHoldemAction = Field(..., description="Action to take")
    amount: int | None = Field(default=None, description="Bet/raise amount", ge=0)

    @model_validator(mode="after")
    def validate_move(self) -> TexasHoldemMove:
        """Validate move."""
        if self.action == TexasHoldemAction.RAISE and self.amount is None:
            raise THErrors.INVALID_ACTION.create(message="Raise action requires an amount", details={"action": self.action.value})
        if self.action in (TexasHoldemAction.FOLD, TexasHoldemAction.CHECK, TexasHoldemAction.CALL, TexasHoldemAction.ALL_IN) and self.amount is not None:
            raise THErrors.INVALID_ACTION.create(message=f"Action {self.action} should not have an amount", details={"action": self.action.value})

        return self


class TexasHoldemEnv(GameEnv[TexasHoldemState, TexasHoldemMove, TexasHoldemConfig]):
    """Environment for Texas Hold'em poker."""

    @override
    def init(self, config: TexasHoldemConfig, player_ids: list[PlayerId], prev_state: TexasHoldemState | None = None) -> TexasHoldemState:
        """Initialize a new Texas Hold'em game state.

        Args:
            config: Game configuration
            player_ids: List of player IDs participating in the game
            prev_state: Optional previous state to continue from (for round transitions)

        Returns:
            New initialized game state
        """

        state: TexasHoldemState
        if prev_state is None:
            # New game - create fresh players
            if len(player_ids) < config.min_players:
                raise THErrors.VALIDATION_ERROR.create(
                    message=f"Not enough players: {len(player_ids)} < {config.min_players}",
                    details={"min_players": config.min_players, "actual_players": len(player_ids)},
                )
            if len(player_ids) > config.max_players:
                raise THErrors.VALIDATION_ERROR.create(
                    message=f"Too many players: {len(player_ids)} > {config.max_players}",
                    details={"max_players": config.max_players, "actual_players": len(player_ids)},
                )

            state = TexasHoldemState(
                game_id=TSID.create().to_string(),
                game_type=config.game_type,
                round_number=1,
                players=[
                    TexasHoldemPlayer(
                        player_id=player_id,
                        chips=config.starting_chips_override_for_test.get(player_id, config.starting_chips)
                        if config.starting_chips_override_for_test
                        else config.starting_chips,
                        status=PlayerStatus.ACTIVE,
                    )
                    for player_id in player_ids
                ],
                dealer_position=0,
            )
        else:
            # Hand transition
            # Find next dealer position among non-OUT players
            current_dealer_position = prev_state.dealer_position
            dealer_position = current_dealer_position
            for i in range(1, len(prev_state.players)):
                next_idx = (current_dealer_position + i) % len(prev_state.players)
                next_player = prev_state.players[next_idx]
                if next_player.status != PlayerStatus.OUT:
                    dealer_position = next_idx
                    break

            if dealer_position == current_dealer_position:
                raise THErrors.VALIDATION_ERROR.create(message="No active players found for new round")

            state = TexasHoldemState(
                game_id=TSID.create().to_string(),
                game_type=prev_state.game_type,
                round_number=prev_state.round_number + 1,
                # Reset player bets and cards, preserve chips and OUT status
                players=[
                    TexasHoldemPlayer(
                        player_id=prev_player.player_id,
                        chips=prev_player.chips,
                        status=PlayerStatus.ACTIVE if prev_player.status != PlayerStatus.OUT else PlayerStatus.OUT,
                    )
                    for prev_player in prev_state.players
                ],
                dealer_position=dealer_position,
            )

        # Initialize round-specific state
        self._init_new_round(state, config)

        # Shuffle deck
        state.deck = self._shuffle_deck()

        # Deal hole cards to active players
        active_indices = [i for i, p in enumerate(state.players) if p.status != PlayerStatus.OUT]
        for player_idx in active_indices:
            player = state.players[player_idx]
            player.hole_cards = [state.deck.pop(), state.deck.pop()]

        return state

    def _shuffle_deck(self) -> list[Card]:
        deck = DECK.copy()
        random.shuffle(deck)
        return deck

    @override
    def apply_move(self, state: TexasHoldemState, move: TexasHoldemMove, config: TexasHoldemConfig) -> TexasHoldemState:
        self._validate_move(state, move)

        # Apply the move on a copy of the state
        state = state.model_copy(deep=True)
        self._apply_move(state, move, config)

        players_in_hand = [p for p in state.players if p.status in (PlayerStatus.ACTIVE, PlayerStatus.ALL_IN)]
        active_players = [p for p in players_in_hand if p.status == PlayerStatus.ACTIVE]

        # Find next player that still needs to act in the current round
        next_action_position = self._find_next_action_position(state, config)
        if next_action_position is not None:
            # This player still needs to act
            state.action_position = next_action_position
            state.current_player_id = state.players[next_action_position].player_id
        else:
            # No players need to act. Everyone has either called, folded, or is all-in. Advance to next round.
            self._advance_betting_round(state, config, players_in_hand=players_in_hand, active_players=active_players)

        if state.betting_round == BettingRound.SHOWDOWN:
            self._finalize_game(state, players_in_hand)

        return state

    def _validate_move(self, state: TexasHoldemState, move: TexasHoldemMove) -> None:
        # Check if game is over
        if state.is_finished:
            raise THErrors.GAME_OVER.create(message="Game has already ended", details={"player_id": move.player_id})

        # Check if betting is allowed in current round
        if state.betting_round == BettingRound.SHOWDOWN:
            raise THErrors.INVALID_ACTION.create(
                message="No moves allowed during showdown",
                details={"player_id": move.player_id, "betting_round": state.betting_round.value},
            )

        # Check if it's the player's turn
        player = state.players[state.action_position]
        if not player or player.player_id != move.player_id:
            raise THErrors.NOT_PLAYER_TURN.create(
                message=f"It's not {move.player_id}'s turn, current player is {player.player_id if player else None}",
                details={"player_id": move.player_id, "current_player": player.player_id if player else None},
            )

        # Check if player is active
        if player.status != PlayerStatus.ACTIVE:
            raise THErrors.INVALID_GAME_STATE.create(
                message=f"Player {move.player_id} is not active ({player.status})",
                details={"player_id": move.player_id, "player_status": player.status.value},
            )

        # Players without chips cannot make moves
        if player.chips == 0:
            raise THErrors.NO_CHIPS.create(message="Player has no chips", details={"player_id": move.player_id})

    def _apply_move(self, state: TexasHoldemState, move: TexasHoldemMove, config: TexasHoldemConfig) -> None:
        player = state.players[state.action_position]

        match move.action:
            case TexasHoldemAction.FOLD:
                player.status = PlayerStatus.FOLDED

            case TexasHoldemAction.CHECK:
                # Can only check if no bet to call
                if state.current_bet > player.current_bet:
                    raise THErrors.CANNOT_CHECK.create(
                        details={"player_id": move.player_id, "current_bet": state.current_bet, "player_bet": player.current_bet},
                    )

                # No other state to change

            case TexasHoldemAction.CALL:
                # Must have a bet to call
                if state.current_bet <= player.current_bet:
                    raise THErrors.NO_BET_TO_CALL.create(
                        details={"player_id": move.player_id, "current_bet": state.current_bet, "player_bet": player.current_bet},
                    )

                call_amount = state.current_bet - player.current_bet
                actual_call = min(call_amount, player.chips)
                # If player doesn't have enough chips, they go all-in
                if actual_call < call_amount:
                    player.status = PlayerStatus.ALL_IN

                player.chips -= actual_call
                player.current_bet += actual_call
                player.total_bet += actual_call

                # Add the call amount to the pot
                state.pot += actual_call

            case TexasHoldemAction.RAISE:
                if move.amount is None:
                    raise THErrors.MISSING_AMOUNT.create(message="Raise requires an amount", details={"player_id": move.player_id})

                previous_bet = state.current_bet

                # Determine minimum raise amount
                min_raise_amount = state.last_raise_amount if state.last_raise_amount > 0 else config.min_raise or config.big_blind
                min_total = previous_bet + min_raise_amount

                if move.amount < min_total:
                    raise THErrors.RAISE_TOO_SMALL.create(
                        message=f"Minimum raise is {min_raise_amount} - must bet at least {min_total}",
                        details={"minimum_raise": min_raise_amount, "minimum_total": min_total, "attempted": move.amount},
                    )

                # Check maximum raise if configured
                if config.max_raise is not None:
                    max_total = state.current_bet + config.max_raise
                    if move.amount > max_total:
                        raise THErrors.RAISE_TOO_LARGE.create(
                            message=f"Raise cannot exceed {config.max_raise}",
                            details={"maximum": config.max_raise, "attempted": move.amount - state.current_bet},
                        )

                # Calculate how much more the player needs to bet to reach the raise amount
                additional_bet = move.amount - player.current_bet
                actual_additional = min(additional_bet, player.chips)

                # If player doesn't have enough chips for full raise, they go all-in
                if actual_additional < additional_bet or actual_additional == player.chips:
                    return self._apply_move(state, TexasHoldemMove(player_id=move.player_id, action=TexasHoldemAction.ALL_IN), config)

                player.chips -= actual_additional
                player.current_bet += actual_additional
                player.total_bet += actual_additional

                # Add the additional bet amount to the pot
                state.pot += actual_additional
                state.current_bet = player.current_bet

                # The last_raise_amount is the conceptual increment of the raise,
                # calculated as the new total bet minus the previous total bet.
                state.last_raise_amount = state.current_bet - previous_bet
                state.last_raise_position = state.action_position

            case TexasHoldemAction.ALL_IN:
                all_in_amount = player.chips
                player.status = PlayerStatus.ALL_IN

                player.chips = 0
                player.current_bet += all_in_amount
                player.total_bet += all_in_amount

                # Add the all-in amount to the pot
                state.pot += all_in_amount

                # Check if this all-in constitutes a raise
                if player.current_bet > state.current_bet:
                    previous_bet = state.current_bet
                    state.current_bet = player.current_bet

                    # Calculate the size of this all-in raise
                    raise_increment = state.current_bet - previous_bet

                    # Determine what a minimum raise would have been at this moment
                    min_raise_amount = state.last_raise_amount if state.last_raise_amount > 0 else config.min_raise or config.big_blind

                    # ONLY update last_raise_amount if the all-in was a full, legal raise.
                    # This is the crucial rule for incomplete raises.
                    if raise_increment >= min_raise_amount:
                        state.last_raise_amount = raise_increment

                    state.last_raise_position = state.action_position

            case _:  # type: ignore
                raise THErrors.INVALID_ACTION.create(message=f"Unknown action: {move.action}", details={"action": move.action.value})

        # Add current position to acted positions
        state.acted_positions.add(state.action_position)
        return None

    def _find_next_action_position(self, state: TexasHoldemState, config: TexasHoldemConfig) -> int | None:
        """Find the next player to act from the current action position in the current round."""

        next_action_position: int | None = None
        for i in range(1, len(state.players)):
            position = (state.action_position + i) % len(state.players)
            next_player = state.players[position]

            # This player needs to act if they are active and haven't acted this round or their bet is less than the current bet.
            if next_player.status == PlayerStatus.ACTIVE and (position not in state.acted_positions or next_player.current_bet < state.current_bet):
                next_action_position = position
                break

        return next_action_position

    def _advance_betting_round(
        self,
        state: TexasHoldemState,
        config: TexasHoldemConfig,
        players_in_hand: list[TexasHoldemPlayer],
        active_players: list[TexasHoldemPlayer],
    ) -> None:
        """Advance to the next betting round."""

        all_in_players = [p for p in players_in_hand if p.status == PlayerStatus.ALL_IN]
        if len(all_in_players) > 0:
            # FIXME: Only do this when someone actually goes all in, not on every round?
            self._create_side_pots(state, players_in_hand)

        cards_to_deal = 0
        next_round: BettingRound

        # If no active players remain, go to showdown.
        # It can be 0 if the player that just acted was the last one to go all-in.
        # It can be 1 if the player that just acted did not go all-in, but everyone else did.
        # If 2 or more active players remain, advance to the next betting round as usual.
        if len(active_players) <= 1 and len(all_in_players) > 0:
            cards_to_deal = 5 - len(state.community_cards)
            next_round = BettingRound.SHOWDOWN
        else:
            match state.betting_round:
                case BettingRound.PREFLOP:
                    cards_to_deal = 3
                    next_round = BettingRound.FLOP
                case BettingRound.FLOP:
                    cards_to_deal = 1
                    next_round = BettingRound.TURN
                case BettingRound.TURN:
                    cards_to_deal = 1
                    next_round = BettingRound.RIVER
                case BettingRound.RIVER:
                    cards_to_deal = 0
                    next_round = BettingRound.SHOWDOWN
                case _:
                    raise THErrors.VALIDATION_ERROR.create(
                        message=f"Invalid betting round: {state.betting_round.value}",
                        details={"betting_round": state.betting_round.value},
                    )

        # Deal community cards
        for _ in range(cards_to_deal):
            state.community_cards.append(state.deck.pop())

        state.betting_round = next_round
        if next_round != BettingRound.SHOWDOWN:
            self._init_new_round(state, config)

    def _init_new_round(self, state: TexasHoldemState, config: TexasHoldemConfig) -> None:
        """Initialize round-specific state including blinds, positions, and betting.

        Args:
            state: The game state to initialize
            config: Game configuration
        """

        # Clear round-specific state
        state.last_raise_amount = 0
        state.last_raise_position = None
        state.acted_positions = set()
        for player in state.players:
            player.current_bet = 0

        active_indices = [i for i, p in enumerate(state.players) if p.status != PlayerStatus.OUT]
        num_active = len(active_indices)

        if num_active < 2:
            raise THErrors.INVALID_GAME_STATE.create(
                message="Not enough active players",
                details={"active_players": num_active},
            )

        state.dealer_position = config.default_dealer_position if state.round_number == 1 else (state.dealer_position + 1) % num_active

        if state.betting_round == BettingRound.PREFLOP:
            if num_active == 2:
                # Special rules for heads-up games (2 active players)
                # In preflop, dealer is small blind and acts first
                state.small_blind_position = state.dealer_position
                state.big_blind_position = active_indices[(active_indices.index(state.dealer_position) + 1) % num_active]
                state.action_position = state.dealer_position
            else:
                # In preflop, small blind is next to dealer, then big blind, then action (UTG)
                dealer_idx_in_active = active_indices.index(state.dealer_position)
                state.small_blind_position = active_indices[(dealer_idx_in_active + 1) % num_active]
                state.big_blind_position = active_indices[(dealer_idx_in_active + 2) % num_active]
                state.action_position = active_indices[(dealer_idx_in_active + 3) % num_active]

            # In preflop, post blinds and deduct from chips
            small_blind_player = state.players[state.small_blind_position]
            big_blind_player = state.players[state.big_blind_position]

            # Handle small blind posting with insufficient chips
            actual_small_blind = min(config.small_blind, small_blind_player.chips)
            small_blind_player.current_bet = actual_small_blind
            small_blind_player.total_bet = actual_small_blind
            small_blind_player.chips -= actual_small_blind
            if actual_small_blind != config.small_blind:
                small_blind_player.status = PlayerStatus.ALL_IN

            # Handle big blind posting with insufficient chips
            actual_big_blind = min(config.big_blind, big_blind_player.chips)
            big_blind_player.current_bet = actual_big_blind
            big_blind_player.total_bet = actual_big_blind
            big_blind_player.chips -= actual_big_blind
            if actual_big_blind != config.big_blind:
                big_blind_player.status = PlayerStatus.ALL_IN

            state.pot = actual_small_blind + actual_big_blind

            # The current bet is still considered the big blind, even if the big blind player did not have enough chips to post it
            state.current_bet = config.big_blind
        else:
            if num_active == 2:
                # Special rules for heads-up games (2 active players)
                # In post-flop, big blind acts first. It must be active otherwise this method wouldn't be called.
                if state.players[state.big_blind_position].status != PlayerStatus.ACTIVE:
                    raise THErrors.INVALID_GAME_STATE.create(
                        message="Big blind is not active",
                        details={"big_blind_position": state.big_blind_position},
                    )
                state.action_position = state.big_blind_position
            else:
                # In post-flop, small blind acts first if they are active, otherwise next active player
                state.action_position = (
                    state.small_blind_position
                    if state.players[state.small_blind_position].status == PlayerStatus.ACTIVE
                    else active_indices[(active_indices.index(state.small_blind_position) + 1) % num_active]
                )

            state.current_bet = 0

        state.current_player_id = state.players[state.action_position].player_id

    def _evaluate_hand(self, cards: list[Card]) -> HandResult:
        """Evaluate a poker hand and return the result."""
        if len(cards) != 7:
            raise ValueError("Hand evaluation requires exactly 7 cards (2 hole cards + 5 community cards)")

        # Check all possible 5-card combinations and return the best hand
        best_result = None

        for five_cards in combinations(cards, 5):
            result = self._evaluate_five_card_hand(list(five_cards))
            if best_result is None or result.rank > best_result.rank or (result.rank == best_result.rank and result.high_cards > best_result.high_cards):
                best_result = result

        # This should never be None since we always have at least a high card
        if best_result is None:
            raise ValueError("Failed to evaluate hand - this should never happen")
        return best_result

    def _evaluate_five_card_hand(self, cards: list[Card]) -> HandResult:
        """Evaluate exactly 5 cards and return the result."""
        # Sort cards by rank in descending order
        sorted_cards = sorted(cards, key=lambda c: c.rank.as_int(), reverse=True)

        # Check for each hand type from highest to lowest
        result = self._check_royal_flush(sorted_cards)
        if result:
            return result

        result = self._check_straight_flush(sorted_cards)
        if result:
            return result

        result = self._check_four_of_a_kind(sorted_cards)
        if result:
            return result

        result = self._check_full_house(sorted_cards)
        if result:
            return result

        result = self._check_flush(sorted_cards)
        if result:
            return result

        result = self._check_straight(sorted_cards)
        if result:
            return result

        result = self._check_three_of_a_kind(sorted_cards)
        if result:
            return result

        result = self._check_two_pair(sorted_cards)
        if result:
            return result

        result = self._check_one_pair(sorted_cards)
        if result:
            return result

        # High card
        return self._check_high_card(sorted_cards)

    def _get_best_five_card_hand(self, cards: list[Card]) -> list[Card]:
        """Get the best 5-card hand from available cards."""
        if len(cards) == 5:
            return cards

        # For now, just return the 5 highest cards
        # In a full implementation, we'd check all combinations
        return sorted(cards, key=lambda c: c.rank.as_int(), reverse=True)[:5]

    def _check_royal_flush(self, cards: list[Card]) -> HandResult | None:
        """Check for royal flush (A, K, Q, J, 10 of same suit)."""
        if not self._is_flush(cards):
            return None

        ranks = [c.rank.as_int() for c in cards]
        if sorted(ranks, reverse=True) == [14, 13, 12, 11, 10]:
            return HandResult(rank=HandRank.ROYAL_FLUSH, high_cards=[14], description="Royal Flush")
        return None

    def _check_straight_flush(self, cards: list[Card]) -> HandResult | None:
        """Check for straight flush."""
        if not self._is_flush(cards):
            return None

        straight_high = self._get_straight_high_card(cards)
        if straight_high:
            return HandResult(rank=HandRank.STRAIGHT_FLUSH, high_cards=[straight_high], description="Straight Flush")
        return None

    def _check_four_of_a_kind(self, cards: list[Card]) -> HandResult | None:
        """Check for four of a kind."""
        rank_counts = self._get_rank_counts(cards)

        for rank, count in rank_counts.items():
            if count == 4:
                kicker = max(r for r, _ in rank_counts.items() if r != rank)
                return HandResult(rank=HandRank.FOUR_OF_A_KIND, high_cards=[rank, kicker], description="Four of a Kind")
        return None

    def _check_full_house(self, cards: list[Card]) -> HandResult | None:
        """Check for full house."""
        rank_counts = self._get_rank_counts(cards)

        trips = None
        pair = None

        for rank, count in sorted(rank_counts.items(), reverse=True):
            if count == 3 and trips is None:
                trips = rank
            elif count == 2 and pair is None:
                pair = rank

        if trips and pair:
            return HandResult(rank=HandRank.FULL_HOUSE, high_cards=[trips, pair], description="Full House")
        return None

    def _check_flush(self, cards: list[Card]) -> HandResult | None:
        """Check for flush."""
        if self._is_flush(cards):
            high_cards = sorted([c.rank.as_int() for c in cards], reverse=True)
            return HandResult(rank=HandRank.FLUSH, high_cards=[high_cards[0]], description="Flush")
        return None

    def _check_straight(self, cards: list[Card]) -> HandResult | None:
        """Check for straight."""
        straight_high = self._get_straight_high_card(cards)
        if straight_high:
            return HandResult(rank=HandRank.STRAIGHT, high_cards=[straight_high], description="Straight")
        return None

    def _check_three_of_a_kind(self, cards: list[Card]) -> HandResult | None:
        """Check for three of a kind."""
        rank_counts = self._get_rank_counts(cards)

        for rank, count in rank_counts.items():
            if count == 3:
                kickers = sorted([r for r, _ in rank_counts.items() if r != rank], reverse=True)
                return HandResult(rank=HandRank.THREE_OF_A_KIND, high_cards=[rank, kickers[0]], description="Three of a Kind")
        return None

    def _check_two_pair(self, cards: list[Card]) -> HandResult | None:
        """Check for two pair."""
        rank_counts = self._get_rank_counts(cards)

        pairs = [rank for rank, count in rank_counts.items() if count == 2]
        if len(pairs) >= 2:
            pairs.sort(reverse=True)
            kicker = max(r for r, _ in rank_counts.items() if r not in pairs[:2])
            return HandResult(rank=HandRank.TWO_PAIR, high_cards=[pairs[0], pairs[1]], description="Two Pair")
        return None

    def _check_one_pair(self, cards: list[Card]) -> HandResult | None:
        """Check for one pair."""
        rank_counts = self._get_rank_counts(cards)

        for rank, count in rank_counts.items():
            if count == 2:
                kickers = sorted([r for r, _ in rank_counts.items() if r != rank], reverse=True)
                return HandResult(rank=HandRank.PAIR, high_cards=[rank, kickers[0]], description="Pair")
        return None

    def _check_high_card(self, cards: list[Card]) -> HandResult:
        """Return high card result."""
        high_cards = sorted([c.rank.as_int() for c in cards], reverse=True)
        return HandResult(rank=HandRank.HIGH_CARD, high_cards=high_cards[:2], description="High Card")

    def _is_flush(self, cards: list[Card]) -> bool:
        """Check if all cards are the same suit."""
        return len({c.suit for c in cards}) == 1

    def _get_straight_high_card(self, cards: list[Card]) -> int | None:
        """Get the high card of a straight, or None if not a straight."""
        ranks = sorted({c.rank.as_int() for c in cards}, reverse=True)

        # Check for regular straight
        if len(ranks) >= 5:
            for i in range(len(ranks) - 4):
                if ranks[i] - ranks[i + 4] == 4:
                    return ranks[i]

        # Check for wheel (A-2-3-4-5)
        if set(ranks) >= {14, 2, 3, 4, 5}:
            return 5  # In wheel, 5 is the high card

        return None

    def _get_rank_counts(self, cards: list[Card]) -> dict[int, int]:
        """Get count of each rank in the hand."""
        counts: dict[int, int] = {}
        for card in cards:
            r = card.rank.as_int()
            counts[r] = counts.get(r, 0) + 1
        return counts

    def _create_side_pots(self, state: TexasHoldemState, players_in_hand: list[TexasHoldemPlayer]) -> None:
        """Create side pots when players have different all-in amounts."""
        all_in_players = [p for p in players_in_hand if p.status == PlayerStatus.ALL_IN]

        # Only create side pots if there are all-in players
        if len(all_in_players) == 0:
            return

        # Get all unique bet amounts from all players (including active players)
        bet_amounts = sorted({p.total_bet for p in players_in_hand if p.total_bet > 0})

        # If all players have the same bet amount, no side pots needed
        if len(bet_amounts) <= 1:
            return

        # Clear existing side pots, we will recalculate them
        state.side_pots = []

        # Create side pots for each betting level
        prev_amount = 0
        for amount in bet_amounts:
            # Find players eligible for this side pot level
            # Players with this bet amount or higher are eligible
            eligible_players = [player.player_id for player in players_in_hand if player.total_bet >= amount]

            # Calculate side pot amount based on the difference from previous level
            level_contribution = amount - prev_amount
            side_pot_amount = level_contribution * len(eligible_players)

            # Only create side pot if there's an amount and eligible players
            if side_pot_amount > 0 and len(eligible_players) > 0:
                side_pot = SidePot(amount=side_pot_amount, eligible_players=eligible_players)
                state.side_pots.append(side_pot)

            prev_amount = amount

        # Adjust main pot to be zero since side pots now contain all the money
        if state.side_pots:
            state.pot = 0

    def _finalize_game(self, state: TexasHoldemState, players_in_hand: list[TexasHoldemPlayer]) -> None:
        """Finalize the game by determining winners and distributing chips."""

        if len(players_in_hand) == 1:
            # Only one player left, they win
            winner = players_in_hand[0]
            state.winners = [winner.player_id]
            # Create a proper HandResult for uncontested win
            uncontested_result = HandResult(
                rank=HandRank.HIGH_CARD,  # Use a default rank
                high_cards=[],
                description="Uncontested",
            )
            state.winning_hands = {winner.player_id: uncontested_result}
        else:
            # Multiple players, evaluate hands for all players in hand
            player_hands: dict[PlayerId, tuple[TexasHoldemPlayer, HandResult]] = {}
            for player in players_in_hand:
                all_cards = player.hole_cards + state.community_cards
                hand_result = self._evaluate_hand(all_cards)
                player_hands[player.player_id] = (player, hand_result)

            # Store all evaluated hands for side pot distribution
            state.winning_hands = {player_id: hand_result for player_id, (_, hand_result) in player_hands.items()}

            # For overall winners (used for display), find the best hand among all players
            best_rank: HandRank = max(hand_result.rank for _, hand_result in player_hands.values())
            best_players: list[tuple[TexasHoldemPlayer, HandResult]] = [
                (player, hand_result) for player, hand_result in player_hands.values() if hand_result.rank == best_rank
            ]

            if len(best_players) == 1:
                # Single overall winner
                winner_player, _ = best_players[0]
                state.winners = [winner_player.player_id]
            else:
                # Multiple players with same rank, compare high cards
                best_high_cards = max(hand_result.high_cards for _, hand_result in best_players)
                final_winners: list[tuple[TexasHoldemPlayer, HandResult]] = [
                    (player, hand_result) for player, hand_result in best_players if hand_result.high_cards == best_high_cards
                ]
                state.winners = [player.player_id for player, _ in final_winners]

        # Distribute chips to winners (handles side pots correctly)
        self._distribute_chips_to_winners(state)

        # Mark players as 'out' if they have no chips
        for player in state.players:
            if player.chips == 0:
                player.status = PlayerStatus.OUT

        state.is_finished = True

    def _distribute_chips_to_winners(self, state: TexasHoldemState) -> None:
        """Distribute chips to winners based on pot and side pots."""
        # Check if chips have already been distributed
        total_pot_amount = state.pot + sum(sp.amount for sp in state.side_pots)
        if total_pot_amount == 0:
            return

        # Distribute main pot if it exists
        if state.pot > 0:
            if not state.winners:
                return
            chips_per_winner = state.pot // len(state.winners)
            remainder = state.pot % len(state.winners)

            for i, winner_id in enumerate(state.winners):
                winner = state.get_player_by_id(winner_id)
                winner.chips += chips_per_winner
                # Give remainder to first winner(s)
                if i < remainder:
                    winner.chips += 1

            state.pot = 0

        # Distribute side pots - each side pot has its own winner determination
        for side_pot in state.side_pots:
            if side_pot.amount == 0:
                continue

            # Find the best hand among players eligible for this side pot
            eligible_hands: dict[PlayerId, HandResult] = {
                player_id: hand_result for player_id, hand_result in state.winning_hands.items() if player_id in side_pot.eligible_players
            }

            if not eligible_hands:
                continue

            # Find the best hand rank among eligible players
            best_rank = max(hand_result.rank for hand_result in eligible_hands.values())
            best_eligible_players = [player_id for player_id, hand_result in eligible_hands.items() if hand_result.rank == best_rank]

            # If multiple players have the same rank, compare high cards
            if len(best_eligible_players) > 1:
                best_high_cards = max(eligible_hands[player_id].high_cards for player_id in best_eligible_players)
                side_pot_winners = [player_id for player_id in best_eligible_players if eligible_hands[player_id].high_cards == best_high_cards]
            else:
                side_pot_winners = best_eligible_players

            # Distribute this side pot among its winners
            if side_pot_winners:
                chips_per_winner = side_pot.amount // len(side_pot_winners)
                remainder = side_pot.amount % len(side_pot_winners)

                for i, winner_id in enumerate(side_pot_winners):
                    winner = state.get_player_by_id(winner_id)
                    winner.chips += chips_per_winner
                    # Give remainder to first winner(s)
                    if i < remainder:
                        winner.chips += 1

                side_pot.amount = 0

        # Clear side pots after distribution
        state.side_pots = []

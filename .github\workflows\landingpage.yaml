# # Sample workflow to access AWS resources when workflow is tied to branch
# # The workflow Creates static website using aws s3
# name: AWS example workflow
# on:
#   push
# env:
#   BUCKET_NAME : "agentleague-landingpage"
#   CLOUDFRONT_DISTRIBUTION_ID : "E23X6BHQCZ8TQV"
#   ROLE-TO-ASSUME: "arn:aws:iam::619403130674:role/github"
#   AWS_REGION : "us-east-1"
# # permission can be added at job level or workflow level
# permissions:
#   id-token: write   # This is required for requesting the JWT
#   contents: read    # This is required for actions/checkout
# jobs:
#   S3PackageUpload:
#     runs-on: ubuntu-latest
#     steps:
#       - name: Git clone the repository
#         uses: actions/checkout@v4
#       - name: configure aws credentials
#         uses: aws-actions/configure-aws-credentials@e3dd6a429d7300a6a4c196c26e071d42e0343502
#         with:
#           role-to-assume: ROLE-TO-ASSUME
#           role-session-name: samplerolesession
#           aws-region: ${{ env.AWS_REGION }}
#       # Upload a file to AWS s3
#       - name: Copy index.html to s3
#         run: |
#           aws s3 cp ./index.html s3://${{ env.BUCKET_NAME }}/

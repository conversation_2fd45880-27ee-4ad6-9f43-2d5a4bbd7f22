[project]
name = "backend"
description = "AgentLeague Backend API"
requires-python = ">=3.13"
version = "0.1.0"
dependencies = [
    "common",
    "shared_db",
    "fastapi[standard]>=0.116.1",
    "uvicorn>=0.35.0",
    "python-multipart>=0.0.17",
]

[project.scripts]
app = "app.main:app"

[tool.coverage.report]
exclude_lines = [
    "if TYPE_CHECKING:",
    "pragma: no cover",
    "if __name__ == .__main__.:",
    "def __repr__",
    "if self\\.debug:",
    "if settings\\.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod"
]
omit = ["*/tests/*"]
show_missing = true

[tool.coverage.run]
branch = true
omit = ["tests/*", "**/*/migrations/**/*.py", "scripts/*"]

[tool.pytest.ini_options]
addopts = "-v --tb=short"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning"
]
testpaths = ["tests"]

[tool.pytest_env]
ENVIRONMENT = "unit_test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]
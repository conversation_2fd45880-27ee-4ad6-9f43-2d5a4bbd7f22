<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Placeholder Logo - Replace with your actual logo -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background -->
  <rect width="200" height="60" rx="12" fill="#F8FAFC" stroke="#E2E8F0" stroke-width="1"/>

  <!-- App Icon -->
  <g transform="translate(15, 15)">
    <!-- Outer ring -->
    <circle cx="15" cy="15" r="14" fill="url(#logoGradient)" opacity="0.1"/>
    <circle cx="15" cy="15" r="14" fill="none" stroke="url(#logoGradient)" stroke-width="2"/>

    <!-- Inner design -->
    <circle cx="15" cy="15" r="8" fill="url(#logoGradient)"/>
    <circle cx="15" cy="15" r="4" fill="white"/>

    <!-- Accent dots -->
    <circle cx="15" cy="7" r="1.5" fill="url(#logoGradient)"/>
    <circle cx="23" cy="15" r="1.5" fill="url(#logoGradient)"/>
    <circle cx="15" cy="23" r="1.5" fill="url(#logoGradient)"/>
    <circle cx="7" cy="15" r="1.5" fill="url(#logoGradient)"/>
  </g>

  <!-- Text -->
  <text x="55" y="25" font-family="system-ui, -apple-system, sans-serif" font-size="16" font-weight="700" fill="#1E293B">
    Agent League
  </text>
  <text x="55" y="40" font-family="system-ui, -apple-system, sans-serif" font-size="11" fill="#64748B">
    Ready for your brand
  </text>

  <!-- Placeholder indicator -->
  <rect x="10" y="50" width="180" height="8" rx="4" fill="#F1F5F9"/>
  <text x="100" y="56" font-family="system-ui, -apple-system, sans-serif" font-size="8" font-weight="500" fill="#94A3B8" text-anchor="middle">
    PLACEHOLDER - REPLACE WITH YOUR LOGO
  </text>
</svg>

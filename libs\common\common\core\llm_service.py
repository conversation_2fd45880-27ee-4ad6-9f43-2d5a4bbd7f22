import json
from enum import Enum
from typing import TypeVar

try:
    import boto3

    _boto3_available = True
except ImportError:
    boto3 = None
    _boto3_available = False

BOTO3_AVAILABLE = _boto3_available

from typing import Any, cast

import pydantic
from pydantic import BaseModel

T = TypeVar("T", bound=BaseModel)

# Type aliases for AWS Bedrock response structures
BedrockResponse = dict[str, object]
ContentItem = dict[str, str]
MessageContent = list[ContentItem]


class ModelFamily(str, Enum):
    CLAUDE = "claude"
    LLAMA = "llama"
    NOVA = "nova"


class ModelName(str, Enum):
    # Claude models
    CLAUDE_3_5_HAIKU = "us.anthropic.claude-3-5-haiku-20241022-v1:0"
    CLAUDE_3_7_SONNET = "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
    CLAUDE_4_SONNET = "us.anthropic.claude-sonnet-4-20250514-v1:0"


class MessageRole(str, Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ContentBlock(BaseModel):
    text: str


class Message(BaseModel):
    role: MessageRole
    content: list[ContentBlock]


class ReasoningConfig(BaseModel):
    enabled: bool = True
    budget_tokens: int = 2000


class LLMConfig(BaseModel):
    max_tokens: int = 10000
    temperature: float = 0.5
    top_p: float = 0.9
    reasoning: ReasoningConfig | None = None


class LLMResponse(BaseModel):
    text: str
    reasoning_text: str | None = None


class LLMClient[T: BaseModel]:
    def __init__(
        self,
        model_id: ModelName,
        region_name: str = "us-east-1",
        config: LLMConfig | None = None,
    ) -> None:
        if not BOTO3_AVAILABLE or boto3 is None:
            raise ImportError(
                "boto3 is required for AWS Bedrock integration. Please install it with 'uv add boto3'.",
            )

        self.model_id = model_id
        self.client = boto3.client("bedrock-runtime", region_name=region_name)
        self.config = config or LLMConfig()

    def _get_model_family(self) -> ModelFamily:
        if "anthropic" in self.model_id:
            return ModelFamily.CLAUDE
        elif "meta" in self.model_id or "llama" in self.model_id:
            return ModelFamily.LLAMA
        elif "amazon" in self.model_id or "nova" in self.model_id:
            return ModelFamily.NOVA
        else:
            raise ValueError(f"Unsupported model: {self.model_id}")

    def _format_instructions(
        self,
        original_message: str,
        response_model: type[T],
    ) -> str:
        return (
            original_message
            + f"\n\n### The output must be a valid json, the first char of the response needs to be {{ and the last char needs to be }}, the response schema is {response_model.model_json_schema()}"
        )

    def _prepare_conversation_api_request(
        self,
        messages: list[Message],
        system_message: str,
    ) -> dict[str, object]:
        return {
            "modelId": self.model_id,
            "system": [
                {
                    "text": system_message,
                },
            ],
            "messages": [
                {
                    "role": msg.role,
                    "content": [{"text": content_block.text} for content_block in msg.content],
                }
                for msg in messages
            ],
            "inferenceConfig": {
                "maxTokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "topP": self.config.top_p,
            },
        }

    def _prepare_native_api_request(
        self,
        message: str,
        max_tokens: int | None = None,
    ) -> dict[str, object]:
        family = self._get_model_family()
        tokens = self.config.max_tokens
        if max_tokens:
            tokens = max_tokens
        if family == ModelFamily.CLAUDE:
            return {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": tokens,
                "temperature": self.config.temperature,
                "messages": [
                    {
                        "role": "user",
                        "content": [{"type": "text", "text": message}],
                    },
                ],
            }

        # Similar implementations for other model families would go here
        raise ValueError(f"Native API not implemented for model family: {family}")

    def _extract_response_text(self, response: BedrockResponse) -> str:
        family = self._get_model_family()

        if family in (ModelFamily.CLAUDE, ModelFamily.LLAMA, ModelFamily.NOVA):
            # For the Conversation API format
            try:
                output = response.get("output", {})
                if isinstance(output, dict):
                    message = cast("dict[str, Any]", output.get("message", {}))
                    content = cast("list[Any]", message.get("content", []))
                    if len(content) > 0:
                        first_content = cast("dict[str, Any]", content[0])
                        text = cast("str", first_content.get("text"))
                        if text:
                            return text
            except (KeyError, IndexError):
                pass

            # For the Native API format
            try:
                content = cast("list[Any]", response.get("content", []))
                if len(content) > 0:
                    first_content = cast("dict[str, Any]", content[0])
                    text = cast("str", first_content.get("text"))
                    if text:
                        return text
            except (KeyError, IndexError, TypeError):
                pass

        raise ValueError(f"Could not extract response text from: {response}")

    def _prepare_reasoning_config(self) -> dict[str, dict[str, str | int]] | None:
        if not self.config.reasoning or not self.config.reasoning.enabled:
            return None

        return {
            "thinking": {
                "type": "enabled",
                "budget_tokens": self.config.reasoning.budget_tokens,
            },
        }

    def _extract_reasoning_text(self, response: BedrockResponse) -> str | None:
        try:
            output = response.get("output", {})
            if isinstance(output, dict):
                message = cast("dict[str, Any]", output.get("message", {}))
                content = cast("list[Any]", message.get("content", []))
                for block in content:
                    block_dict = cast("dict[str, Any]", block)
                    reasoning_content = cast("dict[str, Any]", block_dict.get("reasoningContent"))
                    if reasoning_content:
                        reasoning_text = cast("str", reasoning_content.get("reasoningText"))
                        if reasoning_text:
                            return reasoning_text
        except (KeyError, IndexError, TypeError):
            pass

        return None

    def generate(
        self,
        message: str,
        response_model: type[T],
        max_tokens: int | None = None,
    ) -> T:
        """Generate a response for a single message"""
        previous_errors = []
        max_retries = 3
        for attempt in range(max_retries):
            try:
                formatted_message = self._format_instructions(message, response_model)
                if previous_errors:
                    formatted_message += f"\n\nPrevious errors: {', '.join(previous_errors)}"
                request = self._prepare_native_api_request(
                    message=formatted_message,
                    max_tokens=max_tokens,
                )
                json_request = json.dumps(request)

                response = self.client.invoke_model(
                    modelId=self.model_id,
                    body=json_request,
                )

                model_response = json.loads(response["body"].read())
                response_text = self._extract_response_text(model_response)

                # Parse the response into the provided Pydantic model
                return response_model.model_validate(json.loads(response_text))

            except pydantic.ValidationError as ve:
                error_msg = f"Validation error on attempt {attempt + 1}: {ve!s}"
                previous_errors.append(error_msg)
                if attempt == max_retries - 1:
                    raise Exception(f"Max retries reached. Last error: {error_msg}")
            except Exception as e:
                raise Exception(f"Error invoking model '{self.model_id}': {e}")

        # This should never be reached due to the exception handling above,
        # but we need it to satisfy the type checker
        raise Exception(
            "Unexpected error: generate method completed without returning a value",
        )

    def converse(
        self,
        system_message: str,
        messages: list[Message],
        response_model: type[T],
    ) -> T:
        """Generate a response for a conversation"""
        try:
            system_message = self._format_instructions(system_message, response_model)
            request = self._prepare_conversation_api_request(
                messages=messages,
                system_message=system_message,
            )

            reasoning_config = self._prepare_reasoning_config()
            if reasoning_config:
                request["additionalModelRequestFields"] = reasoning_config

            response = self.client.converse(**cast("Any", request))

            response_text = self._extract_response_text(cast("BedrockResponse", response))
            reasoning_text = self._extract_reasoning_text(cast("BedrockResponse", response)) if self.config.reasoning else None

            # Create a response dict based on the response_model fields
            response_dict: dict[str, str] = {"text": response_text}
            if reasoning_text is not None and "reasoning_text" in response_model.__annotations__:
                response_dict["reasoning_text"] = reasoning_text

            # Parse the response into the provided Pydantic model
            return response_model.model_validate(response_dict)

        except Exception as e:
            raise Exception(f"Error in conversation with model '{self.model_id}': {e}")


class LLMFactory:
    @staticmethod
    def create_client(
        model_name: ModelName,
        region_name: str = "us-west-2",
        config: LLMConfig | None = None,
    ) -> LLMClient[BaseModel]:
        return LLMClient(model_id=model_name, region_name=region_name, config=config)


# Example usage:
# from backend.app.core.llm_service import LLMFactory, ModelName, LLMConfig, ReasoningConfig, LLMResponse
#
# # Create a client with default settings
# client = LLMFactory.create_client(ModelName.CLAUDE_3_HAIKU)
#
# # Create a client with custom settings
# config = LLMConfig(
#     max_tokens=1024,
#     temperature=0.7,
#     top_p=0.95,
#     reasoning=ReasoningConfig(enabled=True, budget_tokens=1000)
# )
# client_with_config = LLMFactory.create_client(
#     model_name=ModelName.CLAUDE_3_7_SONNET,
#     region_name="us-west-2",
#     config=config
# )
#
# # Example custom response model
# class CustomResponse(BaseModel):
#     text: str
#     sentiment: Optional[str] = None
#
# # Generate a response
# response = client.generate("Hello, how are you?", LLMResponse)
# print(response.text)
#
# # Generate a response with a custom model
# custom_response = client.generate("How's the weather today?", CustomResponse)
# print(custom_response.text)

// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-outside-of-docker
{
	"name": "Docker outside of Docker",
	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	"image": "mcr.microsoft.com/devcontainers/base:ubuntu",
	"features": {
		"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {},
		"ghcr.io/devcontainers/features/python:1": {
			"version": "3.11"
		},
		"ghcr.io/devcontainers/features/node:1": {
			"version": "lts" // Installs Node.js LTS, which includes npm
		},
		"ghcr.io/devcontainers/features/aws-cli:1": {},
		"ghcr.io/devcontainers-extra/features/typescript:2": {} // Installs TypeScript
	},
	// Use this environment variable if you need to bind mount your local source code into a new container.
	"remoteEnv": {
		"LOCAL_WORKSPACE_FOLDER": "${localWorkspaceFolder}",
		// Add environment variables to help with host access
		"HOST_IP": "host.docker.internal"
	},
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	"forwardPorts": [
		8000,
		3000,
		5173
	],
	// Configure networking for proper port forwarding
	"runArgs": [
		"--add-host=host.docker.internal:host-gateway",
		"--network=host"
	],
	"mounts": [
		"source=${localEnv:HOME}/.ssh/germanilia,target=/tmp/host-germanilia-key,type=bind,consistency=cached",
		"source=${localEnv:HOME}/.ssh/iliagerman,target=/tmp/host-iliagerman-key,type=bind,consistency=cached"
	],
	// Use 'postCreateCommand' to run commands after the container is created.
	"postCreateCommand": "bash scripts/setup-devcontainer.sh",
	// Configure tool-specific properties.
	"customizations": {
		"vscode": {
			"settings": {
				"terminal.integrated.defaultProfile.linux": "bash",
				"python.defaultInterpreterPath": "/usr/local/python/current/bin/python"
			},
			"extensions": [
				"github.copilot-chat",
				"ms-python.black-formatter",
				"ms-azuretools.vscode-docker",
				"dbaeumer.vscode-eslint",
				"ms-python.vscode-pylance",
				"ms-python.python",
				"ms-python.debugpy",
				"ms-vscode-remote.remote-ssh",
				"ms-vscode-remote.remote-ssh-edit",
				"saoudrizwan.claude-dev",
				"streetsidesoftware.code-spell-checker",
				"danields761.dracula-theme-from-intellij-pythoned",
				"cweijan.vscode-database-client2",
				"janisdd.vscode-edit-csv",
				"streetsidesoftware.code-spell-checker-hebrew",
				"toolsai.vscode-jupyter-cell-tags",
				"magicstack.magicpython",
				"magicstack.magicpython",
				"mechatroner.rainbow-csv",
				"bradlc.vscode-tailwindcss",
				"postman.postman-for-vscode",
				"redhat.vscode-yaml",
				"redhat.vscode-xml",
				"augment.vscode-augment",
				"mhutchie.git-graph",
				"donjayamanne.githistory"
			]
		}
	},
	// Uncomment the next line to run commands as a non-root user. On Linux, you may need to add yourself to the docker group.
	"remoteUser": "root"
	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
}
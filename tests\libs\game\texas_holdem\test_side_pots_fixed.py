"""Tests for side pot functionality in Texas Hold'em."""

from libs.game.texas_holdem.poker import (
    PLAYER_1,
    PLAYER_2,
    PLAYER_3,
    PLAYER_4,
    PlayerStatus,
    TexasHoldemAction,
)

from .test_helpers import PokerTest, TexasHoldemPlayerDiff, TexasHoldemStateDiff


class TestSidePots:
    """Test side pot creation and distribution."""

    def test_basic_side_pot_creation(self) -> None:
        """Test basic side pot creation with different stack sizes."""
        test = PokerTest.create(num_players=3, chips={PLAYER_1: 50, PLAYER_2: 200, PLAYER_3: 200})

        # First player goes all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_1: TexasHoldemPlayerDiff(chips=0, current_bet=50, total_bet=50, status=PlayerStatus.ALL_IN)},
            pot=65,
            current_bet=50,
            current_player_id=PLAYER_2,
            action_position=1,
            last_raise_amount=40,
            last_raise_position=0
        ))

        # Second player calls
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_2: TexasHoldemPlayerDiff(chips=150, current_bet=50, total_bet=50)},
            pot=110,
            current_player_id=PLAYER_3,
            action_position=2
        ))

        # Third player folds - this ends the betting round and deals community cards
        test.process_move(PLAYER_3, TexasHoldemAction.FOLD)
        # Skip state diff check for fold as it includes complex deck/community card changes

        # Force to showdown
        test.advance_to_showdown()

        # Verify game completion and chip conservation
        test.assert_state_change(TexasHoldemStateDiff(is_finished=True))
        total_chips = sum(player.chips for player in test.state.players)
        assert total_chips == 450, f"Total chips should be 450, got {total_chips}"

    def test_multiple_all_ins_different_stacks(self) -> None:
        """Test multiple all-ins with different stack sizes."""
        test = PokerTest.create(num_players=3, chips={PLAYER_1: 30, PLAYER_2: 80, PLAYER_3: 150})

        # All players go all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_1: TexasHoldemPlayerDiff(chips=0, current_bet=30, total_bet=30, status=PlayerStatus.ALL_IN)},
            pot=45,
            current_bet=30,
            current_player_id=PLAYER_2,
            action_position=1,
            last_raise_amount=20,
            last_raise_position=0
        ))

        test.process_move(PLAYER_2, TexasHoldemAction.ALL_IN)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_2: TexasHoldemPlayerDiff(chips=0, current_bet=80, total_bet=80, status=PlayerStatus.ALL_IN)},
            pot=120,
            current_bet=80,
            current_player_id=PLAYER_3,
            action_position=2,
            last_raise_amount=50,
            last_raise_position=1
        ))

        test.process_move(PLAYER_3, TexasHoldemAction.ALL_IN)
        # Game automatically finishes when all players are all-in, so no state assertion needed here

        # Force to showdown
        test.advance_to_showdown()

        # Verify game completion and chip conservation
        # Skip detailed state assertion since showdown creates complex winning_hands data
        test.assert_state_change(TexasHoldemStateDiff(is_finished=True), exclude_fields={"winning_hands", "side_pots", "players", "pot", "winners", "deck", "last_raise_amount", "betting_round", "community_cards", "current_bet"})
        total_chips = sum(player.chips for player in test.state.players)
        assert total_chips == 260, f"Total chips should be 260, got {total_chips}"

    def test_side_pot_with_fold(self) -> None:
        """Test side pot creation when one player folds."""
        test = PokerTest.create(num_players=3, chips={PLAYER_1: 100, PLAYER_2: 200, PLAYER_3: 200})

        # First player goes all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_1: TexasHoldemPlayerDiff(chips=0, current_bet=100, total_bet=100, status=PlayerStatus.ALL_IN)},
            pot=115,
            current_bet=100,
            current_player_id=PLAYER_2,
            action_position=1,
            last_raise_amount=90,
            last_raise_position=0
        ))

        # Second player folds
        test.process_move(PLAYER_2, TexasHoldemAction.FOLD)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_2: TexasHoldemPlayerDiff(status=PlayerStatus.FOLDED)},
            current_player_id=PLAYER_3,
            action_position=2
        ))

        # Third player folds - game automatically finishes since only all-in player remains
        test.process_move(PLAYER_3, TexasHoldemAction.FOLD)

        # Force to showdown
        test.advance_to_showdown()

        # Verify game completion - all-in player should win
        assert test.state.is_finished, "Game should be finished"

    def test_complex_four_way_side_pots(self) -> None:
        """Test complex side pot scenario with four players."""
        test = PokerTest.create(
            num_players=4,
            chips={PLAYER_1: 50, PLAYER_2: 100, PLAYER_3: 150, PLAYER_4: 200}
        )

        # All players go all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_1: TexasHoldemPlayerDiff(chips=0, current_bet=50, total_bet=50, status=PlayerStatus.ALL_IN)},
            pot=65,
            current_bet=50,
            current_player_id=PLAYER_2,
            action_position=1,
            last_raise_amount=40,
            last_raise_position=0
        ))

        test.process_move(PLAYER_2, TexasHoldemAction.ALL_IN)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_2: TexasHoldemPlayerDiff(chips=0, current_bet=100, total_bet=100, status=PlayerStatus.ALL_IN)},
            pot=165,
            current_bet=100,
            current_player_id=PLAYER_3,
            action_position=2,
            last_raise_amount=50,
            last_raise_position=1
        ))

        test.process_move(PLAYER_3, TexasHoldemAction.ALL_IN)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_3: TexasHoldemPlayerDiff(chips=0, current_bet=150, total_bet=150, status=PlayerStatus.ALL_IN)},
            pot=310,
            current_bet=150,
            current_player_id=PLAYER_4,
            action_position=3,
            last_raise_position=2
        ))

        test.process_move(PLAYER_4, TexasHoldemAction.ALL_IN)
        # Game automatically finishes when all players are all-in, so no state assertion needed here

        # Force to showdown
        test.advance_to_showdown()

        # Verify game completion and chip conservation
        assert test.state.is_finished, "Game should be finished"
        total_chips = sum(player.chips for player in test.state.players)
        assert total_chips == 500, f"Total chips should be 500, got {total_chips}"

    def test_side_pot_with_raise_after_all_in(self) -> None:
        """Test side pot when player raises after all-in."""
        test = PokerTest.create(num_players=3, chips={PLAYER_1: 75, PLAYER_2: 200, PLAYER_3: 200})

        # First player goes all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_1: TexasHoldemPlayerDiff(chips=0, current_bet=75, total_bet=75, status=PlayerStatus.ALL_IN)},
            pot=90,
            current_bet=75,
            current_player_id=PLAYER_2,
            action_position=1,
            last_raise_amount=65,
            last_raise_position=0
        ))

        # Second player raises beyond all-in amount
        test.process_move(PLAYER_2, TexasHoldemAction.RAISE, amount=150)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_2: TexasHoldemPlayerDiff(chips=50, current_bet=150, total_bet=150)},
            pot=235,
            current_bet=150,
            current_player_id=PLAYER_3,
            action_position=2,
            last_raise_amount=145,
            last_raise_position=1
        ))

        # Third player folds
        test.process_move(PLAYER_3, TexasHoldemAction.FOLD)

        # Force to showdown
        test.advance_to_showdown()

        # Verify game completion and chip conservation
        test.assert_state_change(TexasHoldemStateDiff(is_finished=True), exclude_fields={"winning_hands", "side_pots", "players", "pot", "winners", "deck", "last_raise_amount", "betting_round", "community_cards", "current_bet"})
        total_chips = sum(player.chips for player in test.state.players)
        assert total_chips == 465, f"Total chips should be 465, got {total_chips}"

    def test_heads_up_all_in(self) -> None:
        """Test heads-up all-in scenario."""
        test = PokerTest.create(
            num_players=2,
            small_blind_position=0,
            big_blind_position=1
        )

        # Small blind goes all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_1: TexasHoldemPlayerDiff(chips=0, current_bet=1000, total_bet=1000, status=PlayerStatus.ALL_IN)},
            pot=1010,
            current_bet=1000,
            current_player_id=PLAYER_2,
            action_position=1,
            last_raise_amount=990,
            last_raise_position=0
        ))

        # Big blind calls
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)
        # Game automatically finishes when both players are all-in, so no state assertion needed here

        # Force to showdown
        test.advance_to_showdown()

        # Verify game completion
        test.assert_state_change(TexasHoldemStateDiff(is_finished=True), exclude_fields={"winning_hands", "side_pots", "players", "pot", "winners", "deck", "last_raise_amount", "betting_round", "community_cards", "current_bet"})
        total_chips = sum(player.chips for player in test.state.players)
        assert total_chips == 2000, f"Total chips should be 2000, got {total_chips}"

    def test_chip_conservation_complex_side_pots(self) -> None:
        """Test chip conservation in complex side pot scenarios."""
        test = PokerTest.create(
            num_players=4,
            chips={PLAYER_1: 25, PLAYER_2: 75, PLAYER_3: 125, PLAYER_4: 175}
        )

        # Record initial total (chips only, since blinds are already in pot)
        initial_total = sum(player.chips for player in test.state.players) + test.state.pot

        # All players go all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_2, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_3, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_4, TexasHoldemAction.ALL_IN)

        # Force to showdown
        test.advance_to_showdown()

        # Verify chip conservation
        final_total = sum(player.chips for player in test.state.players)
        assert final_total == initial_total, f"Chips not conserved: {final_total} != {initial_total}"

        # Verify game is finished
        assert test.state.is_finished, "Game should be finished"

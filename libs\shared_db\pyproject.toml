[project]
name = "shared_db"
description = "Shared database models and utilities"
requires-python = ">=3.13"
version = "0.1.0"
dependencies = [
    "common",
    "sqlalchemy[asyncio]>=2.0.42",
    "alembic>=1.14.0",
    "psycopg2-binary>=2.9.10",
    "asyncpg>=0.30.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["shared_db"]
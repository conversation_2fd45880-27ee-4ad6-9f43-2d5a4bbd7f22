<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Placeholder Logo - Dark Mode Version -->
  <defs>
    <linearGradient id="logoGradientDark" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#60A5FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="200" height="60" rx="12" fill="#0F172A" stroke="#334155" stroke-width="1"/>
  
  <!-- App Icon -->
  <g transform="translate(15, 15)">
    <!-- Outer ring -->
    <circle cx="15" cy="15" r="14" fill="url(#logoGradientDark)" opacity="0.2"/>
    <circle cx="15" cy="15" r="14" fill="none" stroke="url(#logoGradientDark)" stroke-width="2"/>
    
    <!-- Inner design -->
    <circle cx="15" cy="15" r="8" fill="url(#logoGradientDark)"/>
    <circle cx="15" cy="15" r="4" fill="#0F172A"/>
    
    <!-- Accent dots -->
    <circle cx="15" cy="7" r="1.5" fill="url(#logoGradientDark)"/>
    <circle cx="23" cy="15" r="1.5" fill="url(#logoGradientDark)"/>
    <circle cx="15" cy="23" r="1.5" fill="url(#logoGradientDark)"/>
    <circle cx="7" cy="15" r="1.5" fill="url(#logoGradientDark)"/>
  </g>
  
  <!-- Text -->
  <text x="55" y="25" font-family="system-ui, -apple-system, sans-serif" font-size="16" font-weight="700" fill="#F8FAFC">
    Agent League
  </text>
  <text x="55" y="40" font-family="system-ui, -apple-system, sans-serif" font-size="11" fill="#94A3B8">
    Ready for your brand
  </text>
  
  <!-- Placeholder indicator -->
  <rect x="10" y="50" width="180" height="8" rx="4" fill="#1E293B"/>
  <text x="100" y="56" font-family="system-ui, -apple-system, sans-serif" font-size="8" font-weight="500" fill="#64748B" text-anchor="middle">
    PLACEHOLDER - REPLACE WITH YOUR LOGO
  </text>
</svg>

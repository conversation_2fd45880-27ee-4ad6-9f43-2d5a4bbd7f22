{"version": "0.2.0", "configurations": [{"name": "Python Debugger: Current File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal"}, {"name": "Backend", "type": "debugpy", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["app.main:app", "--host", "0.0.0.0", "--port", "9000", "--reload"], "cwd": "${workspaceFolder}/backend", "console": "integratedTerminal", "presentation": {"hidden": false, "group": "servers", "order": 1}}, {"name": "Client", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/client/src", "preLaunchTask": "Start Vite Dev Server", "presentation": {"hidden": false, "group": "servers", "order": 2}}], "compounds": [{"name": "Run Both", "configurations": ["Backend", "Client"], "preLaunchTask": "Start Log Monitor"}]}
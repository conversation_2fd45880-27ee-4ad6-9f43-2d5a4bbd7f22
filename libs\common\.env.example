# Application settings
APP_ENV=development
DEBUG=True
API_PREFIX=/api/v1
PROJECT_NAME=Agent League
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:5173,http://localhost:3000
PORT=9000
HOST=0.0.0.0

# Database settings
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/mydatabase

# AWS Secrets Manager configuration (for production)
# If set, secrets will be loaded from AWS Secrets Manager instead of secrets.yaml
# The secret should contain YAML content with the same structure as secrets.yaml
# AWS_SECRETS_MANAGER_SECRET_NAME=my-app-production-secrets
# AWS_DEFAULT_REGION=us-east-1

# Cognito configuration
COGNITO_REGION=us-east-1
COGNITO_POOL_NAME=MyAppUserPool
COGNITO_CLIENT_NAME=MyAppClient

# Logging settings
LOG_LEVEL=INFO
LOG_JSON_FORMAT=True
LOG_CONSOLE_OUTPUT=True
LOG_FILE_OUTPUT=False
LOG_FILE_PATH=logs/app.log
LOG_ROTATION=20 MB
LOG_RETENTION=1 week
LOG_COMPRESSION=zip

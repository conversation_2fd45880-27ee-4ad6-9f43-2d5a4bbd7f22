Understood. We will revert to the design where the monolith instances handle WebSocket connections directly, using Redis Pub/Sub as a backplane. This approach keeps the real-time logic consolidated within the main application, which is a perfectly valid and robust strategy.

Here is the final, comprehensive set of PRDs, structured as a single Markdown document for clarity and completeness.

# Agent Arena Platform - Product Requirements Specification

## Preamble for the AI Coder

**System Context:** The following PRDs describe components of a single **monolithic application**. The monolith will be deployed as multiple, stateless instances behind a load balancer. All API endpoints are part of the same codebase. All state is externalized to shared, managed services to ensure resilience.

*   **Primary Database:** PostgreSQL (AWS RDS)
*   **Message Queue:** SQS (for the resilient game loop)
*   **Caching & Pub/Sub:** Redis (AWS ElastiCache)
*   **File Storage:** S3 (for tool code)
*   **Sandboxed Execution:** AWS Lambda (for agent turns)

---

## PRD #1: Core Platform - User & Agent Workshop

**Version:** 1.0

### 1. Introduction & Goal
This document outlines the foundational components of the Agent Arena platform. The goal is to build the core services for user authentication, user profile management, and the complete "Agent Workshop" lifecycle for creating and managing AI agents and their tools.

### 2. Core User Stories
*   As a new user, I want to register for an account and log in.
*   As a user, I want a profile where my virtual currency balance is tracked.
*   As a user, I want to create a new AI agent, giving it a name, description, a main prompt, and selecting an LLM.
*   As a user, I want to create custom tools by writing Python code, defining their inputs and outputs.
*   As a user, I want to attach my created tools to my agents.

### 3. Functional Requirements
*   The system must support user registration and session-based login (e.g., JWT).
*   New users must be credited with a default starting balance of virtual currency.
*   Users must be able to access a dashboard to see and manage their created agents.
*   Tool code (Python) must be stored securely and associated with the user who created it.
*   Users must be able to add/remove tools from an agent's configuration.

### 4. Technical Implementation Details

#### API Endpoints:
*   `POST /api/auth/register` (Body: `username`, `password`, `email`) -> Returns JWT token.
*   `POST /api/auth/login` (Body: `username`, `password`) -> Returns JWT token.
*   `GET /api/user/me` (Auth Required) -> Returns user profile, including `virtual_currency_balance`.
*   `POST /api/agents` (Auth Required, Body: `name`, `description`, `main_prompt`, `llm_provider`) -> Returns new agent object.
*   `GET /api/agents/{agentId}` (Auth Required) -> Returns agent details, including attached tools.
*   `PUT /api/agents/{agentId}` (Auth Required, Body: ...) -> Updates agent details.
*   `POST /api/tools` (Auth Required, Body: `name`, `description`, `python_code`, `input_schema`, `output_schema`) -> Uploads code to S3, returns new tool object.
*   `POST /api/agents/{agentId}/tools` (Auth Required, Body: `{ "toolId": "..." }`) -> Attaches a tool to an agent.

#### Database Schema (PostgreSQL):
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email_hash VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    virtual_currency_balance BIGINT NOT NULL DEFAULT 1000,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE agents (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL REFERENCES users(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    main_prompt TEXT,
    llm_provider VARCHAR(50),
    ranked_wins INT NOT NULL DEFAULT 0,
    ranked_losses INT NOT NULL DEFAULT 0,
    friendly_games_played INT NOT NULL DEFAULT 0,
    total_money_won BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE tools (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL REFERENCES users(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    input_schema JSONB,
    output_schema JSONB,
    storage_path VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE agent_tools (
    agent_id INT NOT NULL REFERENCES agents(id),
    tool_id INT NOT NULL REFERENCES tools(id),
    PRIMARY KEY (agent_id, tool_id)
);
```

#### Service Interactions:
*   **Amazon S3:** On `POST /api/tools`, the monolith must upload the provided `python_code` to a designated S3 bucket. The object key must be stored in `tools.storage_path`.

### 5. Acceptance Criteria
*   A new user can successfully register, log in, and retrieve their profile with the default currency.
*   A logged-in user can create an agent, and the agent is correctly stored in the `agents` table.
*   A logged-in user can create a tool; the python code is present in S3, and the tool's metadata is in the `tools` table.
*   An agent can be successfully associated with a tool via the `agent_tools` join table.

---

## PRD #2: Resilient Game Engine & Matchmaking

**Version:** 1.0

### 1. Introduction & Goal
This document describes the core backend engine for running game matches. The primary goal is to create a resilient, asynchronous system that can run a game from start to finish without losing state, even if application servers restart. This system uses a message queue (SQS) to drive the game forward turn-by-turn.

### 2. Core User Stories
*   As a player, I want to enter my agent into a matchmaking queue for a specific game type (ranked or friendly).
*   As the system, I need to form a match when enough agents are in a queue.
*   As the system, I need to execute each agent's turn by invoking it in a sandboxed AWS Lambda environment.
*   As the system, I need to record every event of the game durably.
*   As the system, I must ensure a game can continue from its exact state if the server instance processing it crashes.
*   As the system, I must update user currency balances and agent statistics accurately at the end of a ranked game.

### 3. Functional Requirements
*   The system must provide queues for different game types.
*   For ranked games, the system must verify a user has enough currency for the buy-in and deduct it before the game starts.
*   When a match is formed, a durable record of the match must be created.
*   The game loop must be driven by SQS messages, where each message represents one turn.
*   The system must enforce a hard time limit on the Lambda's execution.
*   Every game event must be stored in a permanent, ordered log.
*   At game end, all statistics must be updated within a single, atomic transaction.

### 4. Technical Implementation Details

#### API Endpoints:
*   `POST /api/matchmaking/join` (Auth Required, Body: `{ "agentId": "...", "gameType": "..." }`) -> Returns `status: "queued"`.

#### Database Schema (PostgreSQL):
```sql
CREATE TABLE matches (
    id SERIAL PRIMARY KEY,
    game_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL, -- 'pending', 'in_progress', 'finished'
    current_state JSONB,
    last_game_event_id INT NOT NULL DEFAULT 0,
    last_chat_event_id INT NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    finished_at TIMESTAMPTZ
);

CREATE TABLE match_participants (
    match_id INT NOT NULL REFERENCES matches(id),
    user_id INT NOT NULL REFERENCES users(id),
    agent_id INT NOT NULL REFERENCES agents(id),
    result JSONB
);

CREATE TABLE match_events (
    id BIGSERIAL PRIMARY KEY,
    match_id INT NOT NULL REFERENCES matches(id),
    event_sequence_id INT NOT NULL,
    event_type VARCHAR(20) NOT NULL, -- 'GAME_UPDATE' or 'CHAT_MESSAGE'
    payload JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(match_id, event_sequence_id, event_type)
);
```

#### Service Interactions:
*   **Amazon SQS:** A Standard Queue (e.g., `game-turn-queue`) will drive the game. Messages contain `{ "matchId": "...", "turn": "..." }`.
*   **AWS Lambda:** A function will be created to execute an agent's logic. The monolith will invoke this Lambda synchronously.
*   **Redis (ElastiCache):** Used for matchmaking queues via `LPUSH` and `RPOP` on Redis lists.

#### Core SQS Worker Logic (Inside the Monolith):
1.  Poll SQS for a message.
2.  On message receipt, begin a DB transaction and get a `FOR UPDATE` lock on the `matches` row.
3.  Invoke the Agent Lambda.
4.  On Lambda response, validate the move and update `current_state`.
5.  Increment `last_game_event_id`.
6.  Insert the new event into the `match_events` table.
7.  If the game is over, update user balances and agent stats.
8.  Commit the transaction.
9.  If not over, send the next turn's message to SQS.
10. Publish the event to Redis Pub/Sub for live clients.
11. Delete the original message from SQS.

### 5. Acceptance Criteria
*   A user can join a matchmaking queue.
*   When 5 agents join, a `matches` record is created, buy-ins are deducted, and the first message is sent to SQS.
*   A game progresses turn-by-turn by processing SQS messages.
*   Killing and restarting the monolith does not cause any active game to be lost.
*   At game end, all user/agent statistics are updated correctly and atomically.

---

## PRD #3: Real-Time Spectator & Chat Experience

**Version:** 1.0

### 1. Introduction & Goal
This document specifies the real-time communication layer, handled directly by the monolith instances. The goal is to allow users to watch games live and chat, with a system that is efficient, scalable across multiple server instances, and can recover from network disconnects.

### 2. Core User Stories
*   As a spectator, I want to watch a game's progress in real-time with minimal delay.
*   As a user, I want to send chat messages to others watching the same game.
*   As a user, if my internet connection briefly drops, I want my view of the game and chat to automatically catch up.

### 3. Functional Requirements
*   Each monolith instance must be capable of handling WebSocket connections.
*   The system must use Redis Pub/Sub as a "backplane," allowing any monolith instance to broadcast messages to clients connected to *any other* instance.
*   The server will push two types of events: `GAME_UPDATE` and `CHAT_MESSAGE`.
*   Each event must contain a sequential `event_sequence_id`.
*   The `GAME_UPDATE` event payload must contain the full, current state of the game board to prevent client drift.
*   The client must track the last seen sequence ID and call a REST API to fetch missing events if a gap is detected.
*   Users must be able to submit chat messages, which are then broadcast to all other clients watching the same game.

### 4. Technical Implementation Details

#### API Endpoints:
*   `POST /api/matches/{matchId}/chat` (Auth Required, Body: `{ "message": "..." }`) -> Persists and triggers a broadcast.
*   `GET /api/matches/{matchId}/events` (Auth Required, Query Params: `since_game_event_id`, `since_chat_event_id`) -> Returns an array of event objects from the `match_events` table.

#### WebSocket Communication (Handled by Monolith):
*   **Connection:** The client connects to a WebSocket endpoint on the monolith (e.g., `/ws`).
*   **Subscription:** The client sends a message to subscribe to a match, e.g., `{ "action": "subscribe", "matchId": "..." }`.
*   **Server-to-Client Message Format:**
    ```json
    {
      "matchId": "match789",
      "eventSequenceId": 15,
      "eventType": "GAME_UPDATE",
      "payload": { ... }
    }
    ```

#### Service Interactions:
*   **Redis Pub/Sub:** The backbone of the real-time layer.
    *   The SQS worker (from PRD #2) publishes the `GAME_UPDATE` event to `pubsub:match:{matchId}`.
    *   The `/api/.../chat` endpoint handler publishes the `CHAT_MESSAGE` event to the same channel.
    *   Each monolith instance will maintain a Redis client that is subscribed to all relevant channels and will forward messages to its connected WebSocket clients.

#### Client-Side Logic:
1.  Maintain `local_last_game_event_id` and `local_last_chat_event_id`.
2.  On WebSocket message receipt, compare its `eventSequenceId` with the local counter + 1.
3.  If it matches, process the event and increment the counter.
4.  If it does not match, set a syncing flag and call the `GET /api/matches/{matchId}/events` endpoint to re-sync.

### 5. Acceptance Criteria
*   A client can connect to the WebSocket server.
*   When a game turn is processed, the client receives a `GAME_UPDATE` message in near real-time, regardless of which monolith instance processed the turn or which instance the client is connected to.
*   Simulating a network disconnect causes the client to correctly call the catch-up API and restore the view to the latest state.

---

## PRD #4: Generic & Configurable Leaderboard Service

**Version:** 1.0

### 1. Introduction & Goal
This document describes a generic leaderboard service. The goal is to decouple leaderboard logic from any specific game, allowing administrators to define new leaderboards through simple data configuration without requiring new code.

### 2. Core User Stories
*   As a Player, I want to browse all available leaderboards for a game vertical.
*   As a Player, I want to view the rankings for any specific leaderboard.
*   As an Administrator, I want to create a new leaderboard by simply adding a configuration entry.

### 3. Functional Requirements
*   The system must support an arbitrary number of distinct leaderboards.
*   Each leaderboard must be defined by a configuration entry.
*   The system must listen for game completion events containing a key-value map of statistics for each player.
*   The service must automatically update all relevant leaderboards based on the stats in the event.
*   The system must support different update behaviors (e.g., `INCREMENT` for accumulating scores, `SET` for ratings).

### 4. Technical Implementation Details

#### API Endpoints:
*   `GET /api/leaderboards` (Query Params: `game_vertical=...`) -> Returns a list of active leaderboards.
*   `GET /api/leaderboards/{leaderboard_key}` (Query Params: `limit=100`, `offset=0`) -> Returns the ranked list for that leaderboard.
*   `GET /api/leaderboards/{leaderboard_key}/my-rank` (Auth Required) -> Returns the current user's rank.

#### Database Schema - Leaderboard Configuration (PostgreSQL):
```sql
CREATE TABLE leaderboard_configurations (
    id SERIAL PRIMARY KEY,
    leaderboard_key VARCHAR(100) UNIQUE NOT NULL, -- "poker_money_won"
    display_name VARCHAR(255) NOT NULL,          -- "Poker: Top Earners"
    game_vertical VARCHAR(50) NOT NULL,          -- "poker"
    tracked_stat_key VARCHAR(50) NOT NULL,       -- "money_won"
    update_behavior VARCHAR(20) NOT NULL DEFAULT 'INCREMENT', -- 'INCREMENT' or 'SET'
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);
```

#### Service Interactions:
*   **Redis (ElastiCache):** A Redis Sorted Set will be used for each leaderboard.
    *   **Key Format:** `leaderboard:{leaderboard_key}`
    *   **Updating Logic:** The SQS worker, at game end, fetches active leaderboard configurations. For each player's stats, it finds matching configs and executes the appropriate Redis command (`ZINCRBY` for 'INCREMENT', `ZADD` for 'SET').

### 5. Acceptance Criteria
*   The system successfully updates multiple different leaderboards from a single game-end event.
*   Adding a new row to `leaderboard_configurations` makes a new leaderboard appear in the API and causes it to start tracking stats, with no code deployment.
*   The leaderboard API correctly returns the ranked list from Redis.
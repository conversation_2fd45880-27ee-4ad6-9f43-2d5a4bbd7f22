from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.dependencies import get_db, get_user_service
from app.services.user_service import UserService
from shared_db.schemas.user import UserResponse

user_router = APIRouter()


@user_router.get("/users/")
async def read_users(
    db: Annotated[Session, Depends(get_db)],
    user_service: Annotated[UserService, Depends(get_user_service)],
    skip: int = 0,
    limit: int = 100,
) -> list[UserResponse]:
    """Retrieve all users."""
    return user_service.get_users(db, skip=skip, limit=limit)


@user_router.get("/users/{user_id}")
async def read_user(
    user_id: int,
    db: Annotated[Session, Depends(get_db)],
    user_service: Annotated[UserService, Depends(get_user_service)],
) -> UserResponse:
    """Retrieve a specific user by ID."""
    user = user_service.get_user_by_id(db, user_id=user_id)
    if user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return user

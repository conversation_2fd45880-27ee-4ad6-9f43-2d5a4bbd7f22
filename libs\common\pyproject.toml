[project]
name = "common"
description = "Common utilities and shared components"
requires-python = ">=3.13"
version = "0.1.0"
dependencies = [
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "python-dotenv>=1.0.1",
    "loguru>=0.7.3",
    "pyyaml>=6.0.2",
    "typer>=0.15.1",
    "boto3>=1.35.84",
    "python-jose[cryptography]>=3.3.0",
    "PyJWT>=2.10.1",
    "requests>=2.32.3",
    "passlib[bcrypt]>=1.7.4",
    "email-validator>=2.2.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["common"]
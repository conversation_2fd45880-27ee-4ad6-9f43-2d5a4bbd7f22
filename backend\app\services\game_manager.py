"""Game manager for orchestrating turn-based games."""

from typing import Generic, TypeVar

from libs.game.game_api.core import <PERSON>Config, GameEnv, GameError, GameState, MoveResult, PlayerMove

# Type variables for generic GameManager
TState = TypeVar("TState", bound=GameState)
TMove = TypeVar("TMove", bound=PlayerMove)
TConfig = TypeVar("TConfig", bound=GameConfig)


class GameManager(Generic[TState, TMove, TConfig]):
    """Manages game flow and coordinates with rule managers.

    This is the main orchestrator that handles game state transitions,
    move validation, and game flow. It delegates rule-specific logic
    to the appropriate GameRuleManager implementation.
    """

    def __init__(self, rule_manager: GameEnv[TState, TMove, TConfig]) -> None:
        """Initialize the game manager.

        Args:
            rule_manager: Game-specific rule manager for validation and state updates
        """
        self.rule_manager = rule_manager

    def process_move(
        self,
        state: TState,
        move: TMove,
        config: TConfig,
    ) -> MoveResult:
        """Process a player move through the game engine.

        Args:
            state: Current game state
            move: Player move to process
            config: Game configuration

        Returns:
            MoveResult containing success status, error details, and new state
        """

        try:
            new_state = self.rule_manager.apply_move(state, move, config)
            return MoveResult(
                success=True,
                error=None,
                new_state=new_state,
            )
        except Exception as e:
            return MoveResult(
                success=False,
                error=GameError(code="MOVE_APPLICATION_ERROR", message=f"Failed to apply move: {e!s}", details={"exception_type": type(e).__name__}),
                new_state=None,
            )

"""Core framework interfaces for the generic turn-based game engine."""

from abc import ABC, abstractmethod
from datetime import UTC, datetime
from enum import Enum
from typing import Any, NewType

from pydantic import BaseModel, Field

from libs.common.common.core.app_error import AppError

PlayerId = NewType("PlayerId", str)

class GameType(str, Enum):
    """Supported game types."""

    TEXAS_HOLDEM = "texas_holdem"


class GameError(BaseModel):
    """Represents an error that occurred during game processing."""

    code: str = Field(..., description="Error code")
    message: str = Field(..., description="Human-readable error message")
    details: dict[str, Any] | None = Field(None, description="Additional error details")


class GameConfig(BaseModel):
    """Base configuration for all games."""

    game_type: GameType = Field(..., description="Type of game")
    max_players: int = Field(..., description="Maximum number of players", ge=2)
    min_players: int = Field(..., description="Minimum number of players", ge=2)

    def model_post_init(self, __context: Any, /) -> None:
        """Validate configuration after initialization."""
        if self.min_players > self.max_players:
            raise ValueError("min_players cannot be greater than max_players")


class GameState(BaseModel):
    """Base game state for all games."""

    game_id: str = Field(..., description="Unique identifier for the game")
    game_type: GameType = Field(..., description="Type of game")
    is_finished: bool = Field(default=False, description="Whether the game has ended")
    current_player_id: PlayerId | None = Field(default=None, description="ID of the player whose turn it is")  # FIXME: Needed?
    round_number: int = Field(default=1, description="Current round number", ge=1)  # FIXME: Needed?


class PlayerMove(BaseModel):
    """Base player move for all games."""

    player_id: PlayerId = Field(..., description="ID of the player making the move")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(UTC), description="Timestamp when move was made")


class MoveResult(BaseModel):
    """Result of processing a player move."""

    success: bool = Field(..., description="Whether the move was successful")
    error: AppError | None = Field(None, description="Error details if move failed")
    new_state: GameState | None = Field(None, description="Updated game state if successful")


class GameEnv[TState: GameState, TMove: PlayerMove, TConfig: GameConfig](ABC):
    """Abstract base class for game-specific rule validation and state management."""

    @abstractmethod
    def init(self, config: TConfig, player_ids: list[PlayerId], prev_state: TState | None = None) -> TState:
        """Initialize a new game state.

        Args:
            config: Game configuration
            player_ids: List of player IDs participating in the game
            prev_state: Optional previous state to continue from

        Returns:
            New initialized game state
        """

    @abstractmethod
    def apply_move(self, state: TState, move: TMove, config: TConfig) -> TState:
        """Apply a validated move to the game state.

        Args:
            state: Current game state
            move: Validated player move
            config: Game configuration

        Returns:
            New game state with move applied
        """

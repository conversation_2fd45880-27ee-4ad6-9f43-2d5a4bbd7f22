exclude = [
  ".bzr",
  ".direnv",
  ".eggs",
  ".git",
  ".hg",
  ".mypy_cache",
  ".nox",
  ".pants.d",
  ".ruff_cache",
  ".svn",
  ".tox",
  ".venv",
  "__pypackages__",
  "_build",
  "buck-out",
  "build",
  "dist",
  "node_modules",
  "venv",
  "__pycache__",
]
fix = true
line-length = 160
target-version = "py313"
cache-dir = ".cache/ruff"

[lint]
fixable = ["ALL"]
select = ["ALL"]
ignore = [
  "B008",    # pyflakes - do not perform function calls in argument defaults
  "E501",    # pycodestyle line too long, handled by black
  "D100",    # pydocstyle - missing docstring in public module
  "D101",    # pydocstyle - missing docstring in public class
  "D102",    # pydocstyle - missing docstring in public method
  "D103",    # pydocstyle - missing docstring in public function
  "D104",    # pydocstyle - missing docstring in public package
  "D105",    # pydocstyle - missing docstring in magic method
  "D106",    # pydocstyle - missing docstring in public nested class
  "D107",    # pydocstyle - missing docstring in __init__
  "D202",    # pydocstyle - no blank lines allowed after function docstring
  "D205",    # pydocstyle - 1 blank line required between summary line and description
  "D415",    # pydocstyle - first line should end with a period, question mark, or exclamation point
  "UP037",   # pyupgrade - removes quotes from type annotation
  "A002",    # flake8-builtins - shadowing a python builtin
  "A003",    # flake8-builtins - class attribute {name} is shadowing a python builtin
  "B010",    # flake8-bugbear - do not call setattr with a constant attribute value
  "RUF012",  # ruff - mutable class attributes should be annotated with `typing.ClassVar`
  "ANN401",  # ruff - Dynamically typed expressions (typing.Any) are disallowed
  "PLR0913", # ruff - Too many arguments to function call
  "PLR2004", # Magic value used in comparison
  "FBT001",  # Boolean typed positional argument in function definition
  "FBT002",  # Boolean default positional argument in function definition
  "FBT003",  # Boolean Boolean default positional argument in function definition
  "ARG002",  # Unused method argument
  "ARG001",  # Unused function argument
  "TD002",
  "TD003",
  "FIX002",
  "PGH003",
  "RUF006",
  "SLF001",
  "PT007",
  "S603",
  "PLW2901", # pylint - for loop variable overwritten by assignment target
  "FBT",
  "PT",
  "TD",
  "PERF203", # ignore for now; investigate
  "G004",    # Logging statement using f-string
  "EM102",   # Exception message uses f-string
  "EM101",   # Exception must not use a string literal
  "TRY003",  # Long message in exception
  "PD011",   # Use .to_numpy()
  "C901",    # Method is too complex
  "PLR0912", # Too many branches
  "PLR0915", # Too many statements
  "PLR0911", # Too many returns
  "RET505",  # Redundant return after else
  "TRY201",  # Redundant exception name in raise
  "BLE001",  # Overly broad exception type
  "TRY002",  # Create your own exception
  "TC001",   # Move first party import to type checking block
  "TC002",
  "TC003",
  "S101",    # Use of assert detected
  "PYI064",
  "B009",
  "TRY300",
  "TRY301", # Abstract raise exceptionstatement
  "COM812",
]

dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[lint.flake8-builtins]
builtins-ignorelist = ["id", "input", "type", "filter"]

[lint.pydocstyle]
convention = "google"

[lint.mccabe]
max-complexity = 12

[lint.pep8-naming]
classmethod-decorators = [
  "classmethod",
  "cached_classmethod",
  "sqlalchemy.ext.declarative.declared_attr",
  "sqlalchemy.orm.declared_attr.directive",
  "sqlalchemy.orm.declared_attr",
]

[lint.isort]
known-first-party = ["tests", "app", "common", "shared_db"]

[lint.per-file-ignores]
"**/migrations/*.py" = ["D104", "D103", "D205", "D212"]
"__init__.py" = ["F401", "D104"]

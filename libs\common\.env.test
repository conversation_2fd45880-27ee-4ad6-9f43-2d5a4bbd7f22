# Test environment configuration
APP_ENV=test
DEBUG=True
LOG_LEVEL=WARNING
ALLOWED_HOSTS=localhost,127.0.0.1

# API settings
API_PREFIX=/api/v1
PROJECT_NAME=Agent League Test

# Server settings
HOST=0.0.0.0
PORT=9001

# CORS settings
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,http://127.0.0.1:5173,http://127.0.0.1:3000,http://localhost:5174,http://127.0.0.1:5174

# Database settings - Use SQLite for testing
DATABASE_URL=sqlite:///./test_database.db

# Cognito settings (test uses mocked Cognito)
USE_MOCK_COGNITO=True
COGNITO_REGION=us-west-2
COGNITO_POOL_NAME=MyAppUserPool-Test
COGNITO_CLIENT_NAME=MyAppClient-Test
COGNITO_USER_POOL_ID=test-pool-id
COGNITO_CLIENT_ID=test-client-id

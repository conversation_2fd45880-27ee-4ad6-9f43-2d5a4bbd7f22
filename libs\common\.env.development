# Development environment configuration
# NO SECRETS SHOULD BE STORED HERE - Use libs/common/secrets.yaml for sensitive information

# Application settings
APP_ENV=development
DEBUG=True
LOG_LEVEL=info
ALLOWED_HOSTS=localhost,127.0.0.1

# API settings
API_PREFIX=/api/v1
PROJECT_NAME=Agent League

# Server settings
HOST=0.0.0.0
PORT=9000

# CORS settings
# Including common development ports for devcontainer and local development
CORS_ORIGINS=http://localhost:5173,http://localhost:3000,http://127.0.0.1:5173,http://127.0.0.1:3000

LOG_LEVEL=INFO
LOG_JSON_FORMAT=True
LOG_CONSOLE_OUTPUT=True
LOG_FILE_OUTPUT=True
LOG_FILE_PATH=logs/app.log
LOG_ROTATION=20 MB
LOG_RETENTION=1 week
LOG_COMPRESSION=zip

# Cognito settings (development uses real A<PERSON> Cognito)
USE_LOCALSTACK=True
LOCALSTACK_ENDPOINT=http://localhost:4566

# Cognito configuration for development
COGNITO_REGION=us-west-2
COGNITO_POOL_NAME=MyAppUserPool-Dev
COGNITO_CLIENT_NAME=MyAppClient-Dev
# Cognito IDs (from create_cognito_development output)
COGNITO_USER_POOL_ID=us-west-2_oiqdXEdz9
COGNITO_CLIENT_ID=5nevv1op695do7n2d6k51clen7

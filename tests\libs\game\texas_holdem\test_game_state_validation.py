"""Tests for game state validation in Texas Hold'em."""

from libs.game.texas_holdem.poker import (
    PLAYER_1,
    PLAYER_2,
    PLAYER_3,
    PLAYER_4,
    PLAYER_5,
    PlayerStatus,
    TexasHoldemAction,
)

from .test_helpers import PokerTest, TexasHoldemPlayerDiff, TexasHoldemStateDiff


class TestGameStateValidation:
    """Test game state validation and integrity checks."""

    def test_invalid_player_count_too_few(self) -> None:
        """Test that game fails with too few players."""
        # Attempt to create game with only 1 player (minimum should be 2)
        try:
            _ = PokerTest.create(num_players=1)
            # If we get here, the setup didn't validate properly
            raise AssertionError("Game should not allow only 1 player")
        except (ValueError, AssertionError) as e:
            # Expected behavior - game should reject invalid player count
            assert "player" in str(e).lower() or "minimum" in str(e).lower()

    def test_invalid_player_count_too_many(self) -> None:
        """Test that game fails with too many players."""
        # Attempt to create game with more than maximum players (typically 10)
        try:
            _ = PokerTest.create(num_players=11)
            # If we get here, the setup didn't validate properly
            raise AssertionError("Game should not allow more than 10 players")
        except (ValueError, AssertionError) as e:
            # Expected behavior - game should reject invalid player count
            assert "player" in str(e).lower() or "maximum" in str(e).lower()

    def test_duplicate_player_positions(self) -> None:
        """Test that game detects duplicate position assignments."""
        test = PokerTest.create()

        # Manually create invalid state with duplicate positions
        original_small_blind = test.state.small_blind_position
        test.state.big_blind_position = (
            test.state.small_blind_position
        )  # Duplicate position

        # Attempt to process a move with invalid state
        test.process_move(PLAYER_1, TexasHoldemAction.CALL)

        # The game should either detect this during move processing or have validation
        # If the game doesn't validate this, we should at least document the behavior
        # For now, we'll restore the valid state to continue testing
        test.state.big_blind_position = (original_small_blind + 1) % len(
            test.state.players
        )

    def test_chip_conservation_during_betting(self) -> None:
        """Test that total chips are conserved during betting rounds."""
        test = PokerTest.create()

        # Calculate initial total chips in the game
        initial_total_chips = sum(player.chips for player in test.state.players)
        initial_total_chips += test.state.pot + sum(
            pot.amount for pot in test.state.side_pots
        )
        # Note: total_bet is already included in the pot, so we don't add it separately

        # Perform several betting actions
        test.process_move(PLAYER_1, TexasHoldemAction.CALL)
        test.process_move(PLAYER_2, TexasHoldemAction.RAISE, amount=20)
        test.process_move(PLAYER_3, TexasHoldemAction.CALL)
        test.process_move(PLAYER_4, TexasHoldemAction.CALL)
        test.process_move(PLAYER_5, TexasHoldemAction.CALL)

        # Calculate total chips after betting
        final_total_chips = sum(player.chips for player in test.state.players)
        final_total_chips += test.state.pot + sum(
            pot.amount for pot in test.state.side_pots
        )
        # Note: total_bet is already included in the pot, so we don't add it separately

        # Total chips should be conserved
        assert initial_total_chips == final_total_chips, (
            f"Chip conservation violated: initial={initial_total_chips}, "
            f"final={final_total_chips}"
        )

    def test_chip_conservation_with_all_ins(self) -> None:
        """Test chip conservation when players go all-in."""
        test = PokerTest.create(
            chips={PLAYER_1: 50, PLAYER_2: 100, PLAYER_3: 150, PLAYER_4: 200, PLAYER_5: 250}
        )

        # Calculate initial total chips
        initial_total_chips = sum(player.chips for player in test.state.players)
        initial_total_chips += test.state.pot + sum(
            pot.amount for pot in test.state.side_pots
        )
        # Note: total_bet is already included in the pot, so we don't add it separately

        # All players go all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_2, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_3, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_4, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_5, TexasHoldemAction.ALL_IN)

        # Calculate final total chips
        final_total_chips = sum(player.chips for player in test.state.players)
        final_total_chips += test.state.pot + sum(
            pot.amount for pot in test.state.side_pots
        )
        # Note: total_bet is already included in the pot, so we don't add it separately

        # Chips should still be conserved
        assert initial_total_chips == final_total_chips, (
            f"Chip conservation violated with all-ins: initial={initial_total_chips}, "
            f"final={final_total_chips}"
        )

    def test_negative_chips_prevention(self) -> None:
        """Test that players cannot have negative chips."""
        test = PokerTest.create(
            chips={PLAYER_1: 5}
        )

        # Attempt a bet larger than available chips (should result in all-in)
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=100)

        # Move should succeed and result in all-in with 0 chips
        test.assert_state_change(
            TexasHoldemStateDiff(
                players={
                    PLAYER_1: TexasHoldemPlayerDiff(
                        chips=0, status=PlayerStatus.ALL_IN
                    )
                }
            )
        )

    def test_invalid_action_position(self) -> None:
        """Test that moves fail when it's not the player's turn."""
        test = PokerTest.create()

        # Attempt move by wrong player (PLAYER_2 when PLAYER_1 should act)
        error = test.process_move_error(PLAYER_2, TexasHoldemAction.CALL)
        assert error is not None
        assert error and (
            "turn" in error.details.message.lower()
            or "action" in error.details.message.lower()
        )

    def test_folded_player_cannot_act(self) -> None:
        """Test that folded players cannot take actions."""
        test = PokerTest.create()

        # Have first player fold
        test.process_move(PLAYER_1, TexasHoldemAction.FOLD)
        test.assert_player_status(PLAYER_1, PlayerStatus.FOLDED)

        # Continue with other players to advance the game
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)
        test.process_move(PLAYER_3, TexasHoldemAction.CHECK)

        # Now try to have the folded player act (should fail)
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error is not None
        assert error and (
            "fold" in error.details.message.lower()
            or "inactive" in error.details.message.lower()
        )

    def test_all_in_player_cannot_act_further(self) -> None:
        """Test that all-in players cannot take further actions."""
        test = PokerTest.create()

        # Have first player go all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.assert_player_status(PLAYER_1, PlayerStatus.ALL_IN)

        # Continue with other players
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)
        test.process_move(PLAYER_3, TexasHoldemAction.CALL)
        test.process_move(PLAYER_4, TexasHoldemAction.CALL)
        test.process_move(PLAYER_5, TexasHoldemAction.CALL)

        # If game continues to next round, all-in player should not be able to act
        if not test.state.is_finished:
            # Try to have all-in player act (should fail)
            error = test.process_move_error(PLAYER_1, TexasHoldemAction.CHECK)
            assert error is not None
            assert (
                error
                and "all" in error.details.message.lower()
                and "in" in error.details.message.lower()
            )

    def test_pot_amount_consistency(self) -> None:
        """Test that pot amounts are consistent with player bets."""
        test = PokerTest.create()

        # Track pot changes through betting
        initial_pot = test.state.pot
        initial_side_pots = sum(pot.amount for pot in test.state.side_pots)
        initial_total_bets = sum(player.total_bet for player in test.state.players)

        # Make some bets
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=20)
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)
        test.process_move(PLAYER_3, TexasHoldemAction.CALL)
        test.process_move(PLAYER_4, TexasHoldemAction.CALL)
        test.process_move(PLAYER_5, TexasHoldemAction.CALL)

        # Calculate expected pot increase
        final_total_bets = sum(player.total_bet for player in test.state.players)
        bet_increase = final_total_bets - initial_total_bets

        # Pot should have increased by the amount of new bets
        final_pot = test.state.pot + sum(pot.amount for pot in test.state.side_pots)
        expected_pot = initial_pot + initial_side_pots + bet_increase

        assert final_pot == expected_pot, (
            f"Pot inconsistency: expected={expected_pot}, actual={final_pot}"
        )

    def test_betting_round_completion_validation(self) -> None:
        """Test that betting rounds complete only when all active players have acted."""
        test = PokerTest.create()

        initial_round = test.state.betting_round

        # Have some players act but not all
        test.process_move(PLAYER_1, TexasHoldemAction.CALL)
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)

        # Betting round should not advance yet (remaining players haven't acted)
        assert test.state.betting_round == initial_round

        # Complete the round
        test.process_move(PLAYER_3, TexasHoldemAction.CALL)
        test.process_move(PLAYER_4, TexasHoldemAction.CALL)
        test.process_move(PLAYER_5, TexasHoldemAction.CHECK)

        # Now betting round should advance (or game should be over)
        assert test.state.betting_round > initial_round or test.state.is_finished

    def test_minimum_bet_validation(self) -> None:
        """Test that minimum bet requirements are enforced."""
        test = PokerTest.create()

        # Attempt a raise that's too small
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.RAISE, amount=1)
        assert error is not None
        assert error and (
            "minimum" in error.details.message.lower()
            or "small" in error.details.message.lower()
        )

        # Valid raise should work
        min_raise = test.config.big_blind * 2  # Typically minimum raise is 2x big blind
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=min_raise)

        # Verify the raise was successful
        test.assert_state_change(
            TexasHoldemStateDiff(
                players={
                    PLAYER_1: TexasHoldemPlayerDiff(
                        total_bet=min_raise
                    )
                },
                current_player_id=PLAYER_2
            )
        )

# Generic Turn-Based Game Engine - Technical Architecture Document

## 1. Architecture Design

```mermaid
graph TD
    A[Client/Agent] --> B[Game API Layer]
    B --> C[GameManager]
    C --> D[GameRuleManager Interface]
    D --> E[TexasHoldemRuleManager]
    C --> F[State Validation]
    C --> G[Move Processing]
    G --> H[Database Layer]
    G --> I[Redis Cache]
    G --> J[Event Publisher]
    
    subgraph "Game Framework Layer"
        C
        F
        G
    end
    
    subgraph "Game Implementation Layer"
        D
        E
    end
    
    subgraph "Data Layer"
        H
        I
    end
    
    subgraph "Event Layer"
        J
    end
```

## 2. Technology Description

- **Backend**: Python 3.11+ with FastAPI, Pydantic v2, SQLAlchemy 2.0
- **Database**: PostgreSQL (existing RDS instance)
- **Cache**: Redis (existing ElastiCache)
- **Testing**: pytest, pytest-asyncio, factory-boy
- **Type Checking**: mypy with strict mode
- **Code Quality**: ruff for linting, black for formatting

## 3. Route Definitions

| Route | Purpose |
|-------|---------|
| POST /api/games/{game_type}/move | Process a player move in a specific game |
| GET /api/games/{game_id}/state | Retrieve current game state |
| POST /api/games/{game_type}/validate | Validate a move without applying it |
| GET /api/games/{game_id}/history | Get complete move history for a game |
| POST /api/games/{game_type}/create | Create a new game instance |
| GET /api/games/types | List all supported game types |

## 4. API Definitions

### 4.1 Core Game API

**Process Move**
```
POST /api/games/{game_type}/move
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| game_id | string | true | Unique identifier for the game instance |
| player_id | string | true | Identifier for the player making the move |
| move_data | object | true | Game-specific move data |
| timestamp | datetime | false | When the move was initiated |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| success | boolean | Whether the move was successfully processed |
| new_state | object | Updated game state (if successful) |
| error | object | Error details (if unsuccessful) |
| game_over | boolean | Whether the game has ended |
| results | object | Game results (if game_over is true) |

Example Request:
```json
{
  "game_id": "game_123",
  "player_id": "player_456",
  "move_data": {
    "action": "raise",
    "amount": 100
  }
}
```

Example Response:
```json
{
  "success": true,
  "new_state": {
    "game_type": "texas_holdem",
    "current_player": "player_789",
    "pot": 250,
    "community_cards": ["AS", "KH", "QD"]
  },
  "error": null,
  "game_over": false,
  "results": null
}
```

**Validate Move**
```
POST /api/games/{game_type}/validate
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| game_state | object | true | Current game state |
| move_data | object | true | Move to validate |
| player_id | string | true | Player attempting the move |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| valid | boolean | Whether the move is valid |
| error | object | Validation error details (if invalid) |
| warnings | array | Non-fatal warnings about the move |

### 4.2 Texas Hold'em Specific API

**Poker Move Data Schema**
```json
{
  "action": "fold|call|raise|check|all_in",
  "amount": 100,  // Required for raise, optional for others
  "metadata": {
    "thinking_time": 5.2,  // Seconds taken to make decision
    "confidence": 0.85     // AI confidence in move (0-1)
  }
}
```

**Poker Game State Schema**
```json
{
  "game_type": "texas_holdem",
  "game_id": "game_123",
  "players": [
    {
      "player_id": "player_456",
      "chips": 1500,
      "hole_cards": ["AS", "KH"],  // Only visible to player
      "position": "dealer",
      "status": "active|folded|all_in",
      "current_bet": 100
    }
  ],
  "community_cards": ["QD", "JC", "10H"],
  "pot": 350,
  "current_bet": 100,
  "minimum_raise": 100,
  "betting_round": "preflop|flop|turn|river",
  "current_player": "player_789",
  "dealer_position": 0,
  "small_blind": 25,
  "big_blind": 50,
  "side_pots": [],
  "hand_number": 15
}
```

## 5. Server Architecture Diagram

```mermaid
graph TD
    A[FastAPI Router] --> B[Game Controller]
    B --> C[GameManager Service]
    C --> D[Move Validator]
    C --> E[State Manager]
    C --> F[Rule Engine]
    F --> G[TexasHoldemRules]
    F --> H[Future Game Rules]
    E --> I[State Repository]
    E --> J[Cache Manager]
    I --> K[(PostgreSQL)]
    J --> L[(Redis)]
    C --> M[Event Publisher]
    M --> N[WebSocket Manager]
    M --> O[Database Events]
    
    subgraph "Controller Layer"
        B
    end
    
    subgraph "Service Layer"
        C
        D
        E
        F
    end
    
    subgraph "Repository Layer"
        I
        J
    end
    
    subgraph "Data Storage"
        K
        L
    end
    
    subgraph "Event Layer"
        M
        N
        O
    end
```

## 6. Data Model

### 6.1 Data Model Definition

```mermaid
erDiagram
    GAME_INSTANCES ||--o{ GAME_MOVES : contains
    GAME_INSTANCES ||--o{ GAME_PLAYERS : has
    GAME_INSTANCES }|--|| GAME_TYPES : is_of_type
    GAME_PLAYERS }|--|| USERS : belongs_to
    GAME_PLAYERS }|--|| AGENTS : controlled_by
    GAME_MOVES }|--|| GAME_PLAYERS : made_by
    
    GAME_TYPES {
        string type_name PK
        string display_name
        jsonb default_config
        boolean is_active
        timestamp created_at
    }
    
    GAME_INSTANCES {
        uuid id PK
        string game_type FK
        jsonb current_state
        string status
        jsonb configuration
        timestamp created_at
        timestamp updated_at
        timestamp finished_at
    }
    
    GAME_PLAYERS {
        uuid id PK
        uuid game_id FK
        int user_id FK
        int agent_id FK
        int position
        string status
        jsonb player_state
        timestamp joined_at
    }
    
    GAME_MOVES {
        uuid id PK
        uuid game_id FK
        uuid player_id FK
        int sequence_number
        jsonb move_data
        jsonb state_before
        jsonb state_after
        boolean is_valid
        jsonb error_details
        timestamp created_at
    }
```

### 6.2 Data Definition Language

**Game Types Table**
```sql
-- Create game types table
CREATE TABLE game_types (
    type_name VARCHAR(50) PRIMARY KEY,
    display_name VARCHAR(100) NOT NULL,
    default_config JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Insert initial game types
INSERT INTO game_types (type_name, display_name, default_config) VALUES
('texas_holdem', 'Texas Hold''em Poker', '{
    "small_blind": 25,
    "big_blind": 50,
    "max_players": 9,
    "starting_chips": 1500,
    "betting_structure": "no_limit"
}');
```

**Game Instances Table**
```sql
-- Create game instances table
CREATE TABLE game_instances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_type VARCHAR(50) NOT NULL REFERENCES game_types(type_name),
    current_state JSONB NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'waiting' 
        CHECK (status IN ('waiting', 'in_progress', 'finished', 'cancelled')),
    configuration JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    finished_at TIMESTAMPTZ
);

-- Create indexes
CREATE INDEX idx_game_instances_type_status ON game_instances(game_type, status);
CREATE INDEX idx_game_instances_created_at ON game_instances(created_at DESC);
CREATE INDEX idx_game_instances_updated_at ON game_instances(updated_at DESC);
```

**Game Players Table**
```sql
-- Create game players table
CREATE TABLE game_players (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID NOT NULL REFERENCES game_instances(id) ON DELETE CASCADE,
    user_id INT NOT NULL REFERENCES users(id),
    agent_id INT NOT NULL REFERENCES agents(id),
    position INT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active'
        CHECK (status IN ('active', 'folded', 'all_in', 'disconnected', 'eliminated')),
    player_state JSONB NOT NULL DEFAULT '{}',
    joined_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(game_id, position),
    UNIQUE(game_id, user_id)
);

-- Create indexes
CREATE INDEX idx_game_players_game_id ON game_players(game_id);
CREATE INDEX idx_game_players_user_id ON game_players(user_id);
CREATE INDEX idx_game_players_agent_id ON game_players(agent_id);
```

**Game Moves Table**
```sql
-- Create game moves table
CREATE TABLE game_moves (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID NOT NULL REFERENCES game_instances(id) ON DELETE CASCADE,
    player_id UUID NOT NULL REFERENCES game_players(id),
    sequence_number INT NOT NULL,
    move_data JSONB NOT NULL,
    state_before JSONB,
    state_after JSONB,
    is_valid BOOLEAN NOT NULL DEFAULT true,
    error_details JSONB,
    processing_time_ms INT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(game_id, sequence_number)
);

-- Create indexes
CREATE INDEX idx_game_moves_game_id_sequence ON game_moves(game_id, sequence_number);
CREATE INDEX idx_game_moves_player_id ON game_moves(player_id);
CREATE INDEX idx_game_moves_created_at ON game_moves(created_at DESC);
CREATE INDEX idx_game_moves_is_valid ON game_moves(is_valid);
```

**Game Statistics Table**
```sql
-- Create game statistics table for analytics
CREATE TABLE game_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    game_id UUID NOT NULL REFERENCES game_instances(id) ON DELETE CASCADE,
    player_id UUID NOT NULL REFERENCES game_players(id),
    statistic_type VARCHAR(50) NOT NULL,
    statistic_value DECIMAL(15,2) NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_game_statistics_game_id ON game_statistics(game_id);
CREATE INDEX idx_game_statistics_player_id ON game_statistics(player_id);
CREATE INDEX idx_game_statistics_type ON game_statistics(statistic_type);
```

**Triggers for Updated Timestamps**
```sql
-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for game_instances
CREATE TRIGGER update_game_instances_updated_at 
    BEFORE UPDATE ON game_instances 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
```

## 7. Implementation Classes

### 7.1 Core Framework Classes

```python
# app/services/game_engine/core.py
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Union, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime
import uuid

class GameType(str, Enum):
    TEXAS_HOLDEM = "texas_holdem"
    # Future game types can be added here

T = TypeVar('T', bound=GameType)

class GameError(BaseModel):
    code: str = Field(..., description="Error code for programmatic handling")
    message: str = Field(..., description="Human-readable error message")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional error context")
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class GameState(BaseModel, Generic[T]):
    game_type: T
    game_id: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        arbitrary_types_allowed = True

class PlayerMove(BaseModel, Generic[T]):
    game_type: T
    player_id: str
    move_data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
class MoveResult(BaseModel):
    success: bool
    new_state: Optional[GameState] = None
    error: Optional[GameError] = None
    game_over: bool = False
    results: Optional[Dict[str, Any]] = None
    processing_time_ms: Optional[int] = None

class GameRuleManager(ABC, Generic[T]):
    """Abstract base class for game-specific rule implementations."""
    
    @abstractmethod
    def validate_move(self, state: GameState[T], move: PlayerMove[T]) -> Union[bool, GameError]:
        """Validate if a move is legal in the current game state."""
        pass
    
    @abstractmethod
    def apply_move(self, state: GameState[T], move: PlayerMove[T]) -> GameState[T]:
        """Apply a validated move to the game state and return new state."""
        pass
    
    @abstractmethod
    def is_game_over(self, state: GameState[T]) -> bool:
        """Check if the game has ended."""
        pass
    
    @abstractmethod
    def get_game_results(self, state: GameState[T]) -> Optional[Dict[str, Any]]:
        """Get final game results if game is over."""
        pass

class GameManager(Generic[T]):
    """Generic game manager that orchestrates game flow."""
    
    def __init__(self, rule_manager: GameRuleManager[T]):
        self.rule_manager = rule_manager
    
    def process_move(self, state: GameState[T], move: PlayerMove[T]) -> MoveResult:
        """Process a player move through the complete validation and application pipeline."""
        start_time = datetime.utcnow()
        
        # Validate game type consistency
        if state.game_type != move.game_type:
            return MoveResult(
                success=False,
                error=GameError(
                    code="GAME_TYPE_MISMATCH",
                    message=f"Move game type {move.game_type} does not match state game type {state.game_type}"
                )
            )
        
        # Validate move against game rules
        validation_result = self.rule_manager.validate_move(state, move)
        if isinstance(validation_result, GameError):
            return MoveResult(
                success=False,
                error=validation_result
            )
        
        # Apply the move
        try:
            new_state = self.rule_manager.apply_move(state, move)
            game_over = self.rule_manager.is_game_over(new_state)
            results = self.rule_manager.get_game_results(new_state) if game_over else None
            
            processing_time = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            return MoveResult(
                success=True,
                new_state=new_state,
                game_over=game_over,
                results=results,
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            return MoveResult(
                success=False,
                error=GameError(
                    code="MOVE_APPLICATION_ERROR",
                    message=f"Failed to apply move: {str(e)}",
                    details={"exception_type": type(e).__name__}
                )
            )
```

### 7.2 Texas Hold'em Implementation

```python
# app/services/game_engine/poker.py
from typing import List, Optional, Dict, Any
from enum import Enum
from .core import GameState, PlayerMove, GameRuleManager, GameError, GameType

class PokerAction(str, Enum):
    FOLD = "fold"
    CALL = "call"
    RAISE = "raise"
    CHECK = "check"
    ALL_IN = "all_in"

class BettingRound(str, Enum):
    PREFLOP = "preflop"
    FLOP = "flop"
    TURN = "turn"
    RIVER = "river"
    SHOWDOWN = "showdown"

class PlayerStatus(str, Enum):
    ACTIVE = "active"
    FOLDED = "folded"
    ALL_IN = "all_in"
    ELIMINATED = "eliminated"

class PokerPlayer(BaseModel):
    player_id: str
    chips: int
    hole_cards: List[str] = Field(default_factory=list)
    position: int
    status: PlayerStatus = PlayerStatus.ACTIVE
    current_bet: int = 0
    total_bet_this_hand: int = 0

class TexasHoldemState(GameState[GameType.TEXAS_HOLDEM]):
    players: List[PokerPlayer]
    community_cards: List[str] = Field(default_factory=list)
    pot: int = 0
    current_bet: int = 0
    minimum_raise: int = 0
    betting_round: BettingRound = BettingRound.PREFLOP
    current_player_index: int = 0
    dealer_position: int = 0
    small_blind: int = 25
    big_blind: int = 50
    side_pots: List[Dict[str, Any]] = Field(default_factory=list)
    hand_number: int = 1
    deck: List[str] = Field(default_factory=list)
    
class PokerMove(PlayerMove[GameType.TEXAS_HOLDEM]):
    action: PokerAction
    amount: Optional[int] = None
    
class TexasHoldemRuleManager(GameRuleManager[GameType.TEXAS_HOLDEM]):
    """Texas Hold'em specific rule implementation."""
    
    def validate_move(self, state: TexasHoldemState, move: PokerMove) -> Union[bool, GameError]:
        """Comprehensive Texas Hold'em move validation."""
        # Implementation will be added in TDD fashion
        pass
    
    def apply_move(self, state: TexasHoldemState, move: PokerMove) -> TexasHoldemState:
        """Apply poker move to game state."""
        # Implementation will be added in TDD fashion
        pass
    
    def is_game_over(self, state: TexasHoldemState) -> bool:
        """Check if poker game has ended."""
        # Implementation will be added in TDD fashion
        pass
    
    def get_game_results(self, state: TexasHoldemState) -> Optional[Dict[str, Any]]:
        """Calculate poker game results."""
        # Implementation will be added in TDD fashion
        pass
```

This technical architecture provides a comprehensive foundation for implementing the generic turn-based game engine with robust Texas Hold'em poker support, following TDD principles and maintaining type safety throughout the system.
{"name": "my-agent-arena-app-client", "version": "1.0.0", "private": true, "type": "module", "scripts": {"start": "vite --host 0.0.0.0", "dev": "vite --host 0.0.0.0", "build": "tsc && vite build", "preview": "vite preview", "test": "playwright test", "test:e2e": "../run-tests.sh", "test:regression": "playwright test --config=playwright.regression.config.ts", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:report": "playwright show-report test-results/html-report", "test:clean": "../run-tests.sh clean"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "autoprefixer": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "postcss": "^8.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.57.0", "react-router-dom": "^7.6.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.0.0", "zod": "^3.25.49"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^18.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^7.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-react": "^7.0.0", "prettier": "^2.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "vite": "^5.4.19"}}
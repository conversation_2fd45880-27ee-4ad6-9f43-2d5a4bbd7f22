"""Example of proper test patterns for Texas Hold'em tests."""

import pytest

from libs.game.texas_holdem.poker import PLAYER_1, PLAYER_2, PLAYER_3, BettingRound, Card, CardRank, CardSuit, PlayerStatus, TexasHoldemAction
from libs.game.texas_holdem.texas_holdem_errors import TexasHoldemErrors as THErrors

from .test_helpers import PokerTest, TexasHoldemPlayerDiff, TexasHoldemStateDiff


class TestProperPatterns:
    """Examples of proper test patterns that should be used throughout."""

    def test_basic_move_with_proper_assert_state_change(self) -> None:
        """Example of proper move testing with assert_state_change."""
        test = PokerTest.create()

        # Make a move and verify state change
        test.process_move(PLAYER_1, TexasHoldemAction.CALL)
        test.assert_state_change(
            TexasHoldemStateDiff(
                players={
                    PLAYER_1: TexasHoldemPlayerDiff(
                        chips=990, current_bet=10, total_bet=10
                    )
                },
                pot=25,
                current_player_id=PLAYER_2,
                action_position=1,
            )
        )

        # Make another move
        test.process_move(PLAYER_2, TexasHoldemAction.RAISE, amount=30)
        test.assert_state_change(
            TexasHoldemStateDiff(
                players={
                    PLAYER_2: TexasHoldemPlayerDiff(
                        chips=970, current_bet=30, total_bet=30
                    )
                },
                pot=55,
                current_bet=30,
                current_player_id=PLAYER_3,
                action_position=2,
                last_raise_amount=30,
                last_raise_position=1
            )
        )

    def test_error_handling_with_proper_error_codes(self) -> None:
        """Example of proper error testing with error codes."""
        test = PokerTest.create()

        # Test invalid action - trying to check when there's a bet
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CHECK)
        assert error.details.code == THErrors.CANNOT_CHECK.code

        # Test raise that's too small (minimum raise validation)
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.RAISE, amount=15)
        assert error.details.code == THErrors.RAISE_TOO_SMALL.code

    def test_error_handling_with_pytest_raises(self) -> None:
        """Example of using pytest.raises for error validation."""
        test = PokerTest.create()

        # Test invalid player action
        with pytest.raises(AssertionError) as exc_info:
            test.process_move(
                PLAYER_2, TexasHoldemAction.CALL
            )  # Not current player's turn

        # Should contain the error message
        assert "It's not player_2's turn" in str(exc_info.value)

    def test_game_setup_with_poker_test_create(self) -> None:
        """Example of proper game setup using PokerTest.create()."""
        # Basic setup
        test = PokerTest.create()
        assert len(test.state.players) == 5  # Default
        assert test.state.current_player_id == PLAYER_1

        # Setup with custom parameters
        test = PokerTest.create(
            num_players=4,
            chips={PLAYER_1: 500, PLAYER_2: 1000},
            small_blind=5,
            big_blind=10
        )
        assert len(test.state.players) == 4
        assert test.config.small_blind == 5
        assert test.config.big_blind == 10

    def test_betting_round_progression(self) -> None:
        """Example of testing betting round progression."""
        test = PokerTest.create(num_players=3)  # Create 3-player game

        # Complete preflop - all players call/check to big blind
        # In 3-player game: Player 1 is dealer, Player 2 is SB, Player 3 is BB
        test.process_move(PLAYER_1, TexasHoldemAction.CALL)  # Dealer calls 10
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)  # SB calls (5 more to make 10)
        test.process_move(PLAYER_3, TexasHoldemAction.CHECK) # BB checks

        # Should advance to flop
        test.assert_state_change(TexasHoldemStateDiff(
            betting_round=BettingRound.FLOP,
            current_bet=0,
            current_player_id=PLAYER_2,  # Small blind acts first post-flop
            action_position=1,
            players={
                PLAYER_1: TexasHoldemPlayerDiff(current_bet=0),
                PLAYER_2: TexasHoldemPlayerDiff(current_bet=0),
                PLAYER_3: TexasHoldemPlayerDiff(current_bet=0)
            }
        ), exclude_fields={"community_cards", "deck"})

    def test_all_in_scenario(self) -> None:
        """Example of testing all-in scenarios."""
        test = PokerTest.create(chips={PLAYER_1: 50})

        # Player goes all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.assert_state_change(
            TexasHoldemStateDiff(
                players={
                    PLAYER_1: TexasHoldemPlayerDiff(
                        chips=0, current_bet=50, total_bet=50, status=PlayerStatus.ALL_IN
                    )
                },
                pot=65,  # 50 + 10 + 5 from blinds
                current_bet=50,
                current_player_id=PLAYER_2,
                action_position=1,
                last_raise_amount=40,
                last_raise_position=0
            )
        )

    def test_side_pot_creation(self) -> None:
        """Example of testing side pot creation."""
        test = PokerTest.create(chips={PLAYER_1: 50, PLAYER_2: 100, PLAYER_3: 100})

        # All players go all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)  # 50 chips
        test.process_move(PLAYER_2, TexasHoldemAction.ALL_IN)  # 100 chips
        test.process_move(PLAYER_3, TexasHoldemAction.ALL_IN)  # 100 chips

        # Force to showdown
        test.advance_to_showdown()

        # Verify game is finished and chips are conserved
        test.assert_state_change(TexasHoldemStateDiff(
            is_finished=True
        ))

        # Verify chip conservation
        total_chips = sum(player.chips for player in test.state.players)
        assert total_chips == 2295, f"Total chips should be 2295, got {total_chips}"

    def test_folding_behavior(self) -> None:
        """Example of testing fold behavior."""
        test = PokerTest.create()

        # Player folds
        test.process_move(PLAYER_1, TexasHoldemAction.FOLD)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_1: TexasHoldemPlayerDiff(status=PlayerStatus.FOLDED)},
            current_player_id=PLAYER_2,
            action_position=1
        ))

        # Folded player should not be able to act
        error = test.process_move_error(PLAYER_1, TexasHoldemAction.CALL)
        assert error.details.code == THErrors.PLAYER_NOT_ACTIVE.code

    def test_chip_conservation(self) -> None:
        """Example of testing chip conservation."""
        test = PokerTest.create()

        # Record initial total
        initial_total = test.get_total_chips_in_play()

        # Make several moves
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=50)
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)
        test.process_move(PLAYER_3, TexasHoldemAction.FOLD)

        # Verify chips are conserved
        final_total = test.get_total_chips_in_play()
        assert final_total == initial_total, f"Chips not conserved: {final_total} != {initial_total}"

    def test_winner_determination(self) -> None:
        """Example of testing winner determination."""
        test = PokerTest.create(
            hole_cards={
                PLAYER_1: [Card.of(CardRank.ACE, CardSuit.HEARTS), Card.of(CardRank.ACE, CardSuit.SPADES)],  # Pocket aces
                PLAYER_2: [Card.of(CardRank.KING, CardSuit.HEARTS), Card.of(CardRank.KING, CardSuit.SPADES)],  # Pocket kings
                PLAYER_3: [Card.of(CardRank.QUEEN, CardSuit.HEARTS), Card.of(CardRank.QUEEN, CardSuit.SPADES)]  # Pocket queens
            }
        )

        # All players go all-in
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_2, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_3, TexasHoldemAction.ALL_IN)

        # Force to showdown
        test.advance_to_showdown()

        # Verify winner
        test.assert_state_change(TexasHoldemStateDiff(
            is_finished=True
        ))

    def test_multiple_raises(self) -> None:
        """Example of testing multiple raises."""
        test = PokerTest.create()

        # First raise
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=30)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_1: TexasHoldemPlayerDiff(chips=970, current_bet=30, total_bet=30)},
            pot=45,
            current_bet=30,
            current_player_id=PLAYER_2,
            action_position=1,
            last_raise_amount=30,
            last_raise_position=0
        ))

        # Re-raise
        test.process_move(PLAYER_2, TexasHoldemAction.RAISE, amount=60)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_2: TexasHoldemPlayerDiff(chips=940, current_bet=60, total_bet=60)},
            pot=105,
            current_bet=60,
            current_player_id=PLAYER_3,
            action_position=2,
            last_raise_amount=60,
            last_raise_position=1
        ))

    def test_heads_up_scenario(self) -> None:
        """Example of testing heads-up scenarios."""
        test = PokerTest.create(
            num_players=2,
            small_blind_position=0,
            big_blind_position=1
        )

        # In heads-up, small blind acts first preflop
        assert test.state.current_player_id == PLAYER_1
        assert test.state.action_position == 0

        # Small blind calls
        test.process_move(PLAYER_1, TexasHoldemAction.CALL)
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_1: TexasHoldemPlayerDiff(chips=990, current_bet=10, total_bet=10)},
            pot=20,
            current_player_id=PLAYER_2,
            action_position=1
        ))

    def test_minimum_raise_validation(self) -> None:
        """Example of testing minimum raise validation."""
        test = PokerTest.create()

        # First raise
        test.process_move(PLAYER_1, TexasHoldemAction.RAISE, amount=30)

        # Try to raise by less than minimum (should fail)
        error = test.process_move_error(PLAYER_2, TexasHoldemAction.RAISE, amount=40)  # Only 10 more
        assert error.details.code == THErrors.RAISE_TOO_SMALL.code

        # Valid minimum raise (should succeed)
        test.process_move(PLAYER_2, TexasHoldemAction.RAISE, amount=60)  # 30 more, matching previous raise
        test.assert_state_change(TexasHoldemStateDiff(
            players={PLAYER_2: TexasHoldemPlayerDiff(chips=940, current_bet=60, total_bet=60)},
            pot=105,
            current_bet=60,
            current_player_id=PLAYER_3,
            action_position=2,
            last_raise_amount=60,
            last_raise_position=1
        ))

    def test_game_completion(self) -> None:
        """Example of testing game completion."""
        test = PokerTest.create()

        # Fast-forward to showdown scenario
        test.process_move(PLAYER_1, TexasHoldemAction.ALL_IN)
        test.process_move(PLAYER_2, TexasHoldemAction.CALL)
        test.process_move(PLAYER_3, TexasHoldemAction.FOLD)

        # Force to completion
        test.advance_to_showdown()

        # Verify game is finished
        test.assert_state_change(TexasHoldemStateDiff(
            is_finished=True
        ))

        # Verify winners are determined
        assert test.state.winners is not None
        assert len(test.state.winners) > 0

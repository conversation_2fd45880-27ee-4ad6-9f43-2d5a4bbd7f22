#!/bin/bash

# DevContainer Post-Create Setup Script
# This script sets up the development environment after the container is created

set -e  # Exit on any error

echo "🚀 Starting DevContainer setup..."

# Add host.docker.internal to /etc/hosts for proper networking
echo "📝 Configuring host networking..."
echo 'host.docker.internal host-gateway' >> /etc/hosts

# Verify Docker installation
echo "🐳 Verifying Docker installation..."
docker --version

# Fix GPG key issues and update package lists
echo "📦 Fixing GPG keys and updating package lists..."

# Fix GPG keys for repositories
echo "🔑 Fixing GPG keys for package repositories..."

# Remove all problematic repository sources temporarily
sudo rm -f /etc/apt/sources.list.d/microsoft-prod.list
sudo rm -f /etc/apt/sources.list.d/microsoft.list
sudo rm -f /etc/apt/sources.list.d/yarn.list

# Fix GPG keyring issues
echo "🔧 Fixing GPG keyring..."
sudo apt-get clean
sudo rm -rf /var/lib/apt/lists/*
sudo mkdir -p /var/lib/apt/lists/partial

# Update with minimal repositories first (skip problematic ones)
echo "📦 Updating with basic repositories..."
sudo apt-get update --allow-insecure-repositories -o Acquire::AllowInsecureRepositories=true || true

# Install essential packages without GPG verification for now
echo "🔧 Installing essential packages..."
sudo apt-get install -y --allow-unauthenticated curl wget gnupg2 software-properties-common apt-transport-https ca-certificates || true

# Install Just (command runner)
echo "⚡ Installing Just command runner..."
curl --proto '=https' --tlsv1.2 -sSf https://just.systems/install.sh | bash -s -- --to /usr/local/bin

# Install system dependencies (allow unauthenticated for DevContainer)
echo "🔧 Installing system dependencies..."
sudo apt-get install -y --allow-unauthenticated libpq-dev python3-dev gcc iputils-ping build-essential || true

# Install uv first
echo "📦 Installing uv package manager..."
curl -LsSf https://astral.sh/uv/install.sh | sh
export PATH="$HOME/.local/bin:$PATH"

# Install Python dependencies using uv
echo "🐍 Installing Python dependencies with uv..."

# Install all workspace dependencies
echo "📦 Installing workspace dependencies..."
uv sync

# Verify installation
echo "✅ Verifying installation..."
uv run --package backend python -c "import fastapi; print('✓ FastAPI installed')"
uv run --package backend python -c "import uvicorn; print('✓ Uvicorn installed')"
uv run --package backend python -c "from common.core.config_service import ConfigService; print('✓ Common library installed')"
uv run --package backend python -c "from shared_db.models import Base; print('✓ Shared DB library installed')"

echo "🎉 All Python dependencies installed successfully!"

# Install frontend dependencies if client directory exists
if [ -d 'client' ]; then
    echo "🎨 Installing frontend dependencies..."
    cd client && npm install && npx playwright install-deps && npx playwright install && cd ..
else
    echo "⚠️  No client directory found, skipping npm install..."
fi

# Configure Git
echo "🔧 Configuring Git..."
git config --global user.email '<EMAIL>'
git config --global user.name 'Ilia German'

# Install LocalStack CLI
echo "☁️  Installing LocalStack CLI..."
curl --output localstack-cli-4.4.0-linux-arm64-onefile.tar.gz \
     --location https://github.com/localstack/localstack-cli/releases/download/v4.4.0/localstack-cli-4.4.0-linux-arm64-onefile.tar.gz
sudo tar xvzf localstack-cli-4.4.0-linux-arm64-onefile.tar.gz -C /usr/local/bin
rm localstack-cli-4.4.0-linux-arm64-onefile.tar.gz

# Install AWS CLI Local
echo "🌐 Installing AWS CLI Local..."
uv tool install awscli-local

# Verify installations
echo "✅ Verifying installations..."
localstack --version
awslocal --version

echo "🎉 DevContainer setup completed successfully!"

[tool:pytest]
# Pytest configuration for backend tests
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings
env =
    APP_ENV = test
    DATABASE_URL = sqlite:///./test_backend.db
    USE_MOCK_COGNITO = True
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    asyncio: Async tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    env_file:
      - libs/common/.env.development
    ports:
      - "9000:9000"
    depends_on:
      - db

  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    depends_on:
      - backend

  db:
    image: postgres:latest
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
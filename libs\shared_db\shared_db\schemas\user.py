"""User schemas for shared database operations."""

from pydantic import BaseModel, ConfigDict, EmailStr

from shared_db.models.user import UserRole


class UserBase(BaseModel):
    """Base user schema with common fields."""

    username: str
    email: EmailStr
    full_name: str | None = None


class UserCreate(UserBase):
    """Schema for creating a new user."""

    role: UserRole | None = UserRole.USER
    cognito_sub: str | None = None


class UserUpdate(BaseModel):
    """Schema for updating a user."""

    username: str | None = None
    email: EmailStr | None = None
    full_name: str | None = None
    is_active: bool | None = None
    role: UserRole | None = None
    cognito_sub: str | None = None


class UserResponse(UserBase):
    """Schema for user responses."""

    id: int
    is_active: bool
    role: UserRole
    cognito_sub: str | None = None

    model_config = ConfigDict(from_attributes=True)


class UserInDB(UserResponse):
    """Schema for user data as stored in database."""

The project is a monorepo and uses UV for dependency management.
Always make sure ruff lint and pyright checks pass after your changes.
Do not disable lint rules, fix the code instead.
Always make sure all tests pass after your changes.

When writing tests:
It should be player 1's turn to act by default
Only specify the number of players if it is significant to the test scenario, otherwise don't specify it.
Never write tests with conditional logic in them. Tests must always be explicit and know what the expected behavior is.
Write tests with STRONG assertions that don't leave room for errors. Prefer asserting full objects completely, not just parts of them.
When fixing a broken test you must first fully understand why its failing and whether the test is incorrect or it actually found a regression in the production code. Fully inspect the production code and fully understand what the test is trying to do, and then decide whether the test is incorrect or the production code is broken. If the test is incorrect, fix the test. If the production code is broken, fix the production code.




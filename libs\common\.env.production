# Production environment configuration
# NO SECRETS SHOULD BE STORED HERE - Use libs/common/secrets.yaml for sensitive information

# Application settings
APP_ENV=production
DEBUG=False
LOG_LEVEL=warning
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com

# API settings
API_PREFIX=/api/v1
PROJECT_NAME=Agent League

# Server settings
HOST=0.0.0.0
PORT=9000

# CORS settings
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# AWS Secrets Manager configuration
# If set, secrets will be loaded from AWS Secrets Manager instead of secrets.yaml
# The secret should contain YAML content with the same structure as secrets.yaml
# AWS_SECRETS_MANAGER_SECRET_NAME=my-app-production-secrets
# AWS_DEFAULT_REGION=us-east-1

LOG_LEVEL=INFO
LOG_JSON_FORMAT=True
LOG_CONSOLE_OUTPUT=True
LOG_FILE_OUTPUT=False
LOG_FILE_PATH=logs/app.log
LOG_ROTATION=20 MB
LOG_RETENTION=1 week
LOG_COMPRESSION=zip

# Cognito settings (production uses real AWS Cognito)
USE_LOCALSTACK=False

# Cognito configuration for production
COGNITO_REGION=us-east-1
COGNITO_POOL_NAME=MyAppUserPool-Prod
COGNITO_CLIENT_NAME=MyAppClient-Prod